"""
Setup script for the RPG Engine package.
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="rpg-engine",
    version="1.0.0",
    author="RPG Engine Team",
    author_email="<EMAIL>",
    description="Collaborative story creation engine built with LangGraph",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/rpg-engine",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Games/Entertainment :: Role-Playing",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=8.0.0",
            "pytest-asyncio>=0.23.0",
            "pytest-cov>=4.0.0",
            "black>=24.0.0",
            "isort>=5.13.0",
            "mypy>=1.8.0",
            "flake8>=6.0.0",
        ],
        "api": [
            "fastapi>=0.110.0",
            "uvicorn>=0.27.0",
        ],
        "cli": [
            "typer>=0.12.0",
            "rich>=13.7.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "rpg-engine=rpg_engine.cli.main:app",
        ],
    },
    include_package_data=True,
    package_data={
        "rpg_engine": ["*.yaml", "*.json", "*.txt"],
    },
    keywords="rpg, ai, storytelling, langgraph, langchain, game, narrative",
    project_urls={
        "Bug Reports": "https://github.com/your-org/rpg-engine/issues",
        "Source": "https://github.com/your-org/rpg-engine",
        "Documentation": "https://rpg-engine.readthedocs.io/",
    },
)
