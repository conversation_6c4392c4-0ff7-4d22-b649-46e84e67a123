"""
Tests for the memory management system.
"""

import pytest
import tempfile
import os
from unittest.mock import Mock

from rpg_engine.memory.manager import MemoryManager
from rpg_engine.database import init_database
from langchain_core.messages import HumanMessage, AIMessage


@pytest.fixture
def temp_db():
    """Create a temporary database for testing."""
    with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as f:
        db_path = f.name
    
    database_url = f"sqlite:///{db_path}"
    init_database(database_url, reset=True)
    
    yield database_url
    
    # Cleanup
    if os.path.exists(db_path):
        os.unlink(db_path)


@pytest.fixture
def mock_llm():
    """Create a mock LLM for testing."""
    llm = Mock()
    llm.invoke.return_value = Mock(content="Test summary of the conversation.")
    return llm


@pytest.fixture
def memory_manager(temp_db, mock_llm):
    """Create a memory manager instance for testing."""
    return MemoryManager(mock_llm)


class TestMemoryManager:
    """Test cases for the memory manager."""
    
    def test_initialization(self, memory_manager):
        """Test memory manager initialization."""
        assert memory_manager is not None
        assert memory_manager.llm is not None
        assert memory_manager.max_conversation_length == 50
        assert memory_manager.summary_overlap == 10
    
    def test_store_memory(self, memory_manager):
        """Test storing a memory."""
        session_id = "test_session"
        
        success = memory_manager.store_memory(
            session_id=session_id,
            memory_type="character",
            title="Character Creation",
            content="Player created a character named Alice, a brave warrior.",
            importance=7.0,
            related_characters=["Alice"],
            tags=["character_creation", "alice"]
        )
        
        assert success is True
    
    def test_store_low_importance_memory(self, memory_manager):
        """Test that low importance memories are not stored."""
        session_id = "test_session"
        
        success = memory_manager.store_memory(
            session_id=session_id,
            memory_type="event",
            title="Minor Event",
            content="A bird flew by.",
            importance=1.0  # Below threshold
        )
        
        assert success is False
    
    def test_retrieve_memories(self, memory_manager):
        """Test retrieving memories."""
        session_id = "test_session"
        
        # Store some test memories
        memory_manager.store_memory(
            session_id=session_id,
            memory_type="character",
            title="Alice the Warrior",
            content="Alice is a brave warrior with a noble heart.",
            importance=8.0,
            related_characters=["Alice"]
        )
        
        memory_manager.store_memory(
            session_id=session_id,
            memory_type="world",
            title="Village of Millbrook",
            content="A peaceful village with friendly inhabitants.",
            importance=6.0,
            related_locations=["Millbrook"]
        )
        
        # Retrieve memories
        memories = memory_manager.retrieve_memories(
            session_id=session_id,
            query="Alice warrior",
            limit=5
        )
        
        assert isinstance(memories, list)
        # Should find at least the Alice memory
        alice_found = any("Alice" in mem.get("title", "") for mem in memories)
        assert alice_found
    
    def test_summarize_conversation(self, memory_manager):
        """Test conversation summarization."""
        messages = [
            HumanMessage(content="Hello, I want to create a character."),
            AIMessage(content="Great! What's your character's name?"),
            HumanMessage(content="I'll call him Bob."),
            AIMessage(content="Excellent! Bob is a great name. What's his background?"),
            HumanMessage(content="He's a former soldier turned merchant."),
            AIMessage(content="Interesting! Bob the merchant has a military background.")
        ]
        
        summary = memory_manager.summarize_conversation(messages)
        
        assert summary is not None
        assert len(summary) > 0
        assert isinstance(summary, str)
    
    def test_get_memory_stats(self, memory_manager):
        """Test getting memory statistics."""
        session_id = "test_session"
        
        # Store some memories
        memory_manager.store_memory(
            session_id=session_id,
            memory_type="character",
            title="Test Character",
            content="A test character for statistics.",
            importance=5.0
        )
        
        memory_manager.store_memory(
            session_id=session_id,
            memory_type="world",
            title="Test Location",
            content="A test location for statistics.",
            importance=6.0
        )
        
        stats = memory_manager.get_memory_stats(session_id)
        
        assert isinstance(stats, dict)
        assert "total_memories" in stats
        assert "memory_types" in stats
        assert "average_importance" in stats
        assert stats["total_memories"] >= 2
    
    def test_messages_to_text(self, memory_manager):
        """Test converting messages to text."""
        messages = [
            HumanMessage(content="Hello there!"),
            AIMessage(content="Hello! How can I help you?"),
            HumanMessage(content="I want to play a game.")
        ]
        
        text = memory_manager._messages_to_text(messages)
        
        assert "Player: Hello there!" in text
        assert "Game Master: Hello! How can I help you?" in text
        assert "Player: I want to play a game." in text


class TestMemoryRetrieval:
    """Test memory retrieval functionality."""
    
    def test_keyword_search(self, memory_manager):
        """Test keyword-based memory search."""
        session_id = "test_session"
        
        # Store memories with different content
        memory_manager.store_memory(
            session_id=session_id,
            memory_type="character",
            title="Dragon Slayer",
            content="A legendary warrior who defeated the ancient dragon.",
            importance=9.0
        )
        
        memory_manager.store_memory(
            session_id=session_id,
            memory_type="world",
            title="Dragon's Lair",
            content="A dark cave where the dragon once lived.",
            importance=7.0
        )
        
        memory_manager.store_memory(
            session_id=session_id,
            memory_type="event",
            title="Village Festival",
            content="The annual harvest festival in the village.",
            importance=4.0
        )
        
        # Search for dragon-related memories
        dragon_memories = memory_manager.retrieve_memories(
            session_id=session_id,
            query="dragon",
            limit=10
        )
        
        assert len(dragon_memories) >= 2
        dragon_titles = [mem["title"] for mem in dragon_memories]
        assert "Dragon Slayer" in dragon_titles
        assert "Dragon's Lair" in dragon_titles
    
    def test_memory_type_filtering(self, memory_manager):
        """Test filtering memories by type."""
        session_id = "test_session"
        
        # Store different types of memories
        memory_manager.store_memory(
            session_id=session_id,
            memory_type="character",
            title="Hero Character",
            content="The main character of our story.",
            importance=8.0
        )
        
        memory_manager.store_memory(
            session_id=session_id,
            memory_type="world",
            title="Fantasy World",
            content="A magical world full of wonders.",
            importance=7.0
        )
        
        # Retrieve only character memories
        character_memories = memory_manager.retrieve_memories(
            session_id=session_id,
            query="character",
            memory_types=["character"],
            limit=10
        )
        
        assert len(character_memories) >= 1
        assert all(mem["type"] == "character" for mem in character_memories)


if __name__ == "__main__":
    pytest.main([__file__])
