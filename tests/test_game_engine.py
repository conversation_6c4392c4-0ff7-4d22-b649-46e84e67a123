"""
Tests for the RPG game engine.
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch

from rpg_engine.core.game_engine import RPGEngine
from rpg_engine.core.state import GameState, ActionType, GamePhase, create_initial_state
from rpg_engine.database import init_database


@pytest.fixture
def temp_db():
    """Create a temporary database for testing."""
    with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as f:
        db_path = f.name
    
    database_url = f"sqlite:///{db_path}"
    init_database(database_url, reset=True)
    
    yield database_url
    
    # Cleanup
    if os.path.exists(db_path):
        os.unlink(db_path)


@pytest.fixture
def mock_llm():
    """Create a mock LLM for testing."""
    llm = Mock()
    llm.invoke.return_value = Mock(content="Test response from LLM")
    return llm


@pytest.fixture
def rpg_engine(temp_db, mock_llm):
    """Create an RPG engine instance for testing."""
    with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as f:
        checkpointer_path = f.name
    
    engine = RPGEngine(
        llm=mock_llm,
        database_url=temp_db,
        checkpointer_path=checkpointer_path
    )
    
    yield engine
    
    # Cleanup
    if os.path.exists(checkpointer_path):
        os.unlink(checkpointer_path)


class TestGameEngine:
    """Test cases for the RPG game engine."""
    
    def test_engine_initialization(self, rpg_engine):
        """Test that the engine initializes correctly."""
        assert rpg_engine is not None
        assert rpg_engine.llm is not None
        assert rpg_engine.memory_manager is not None
        assert rpg_engine.story_editor is not None
        assert rpg_engine.graph is not None
    
    def test_start_new_game(self, rpg_engine):
        """Test starting a new game session."""
        user_id = "test_user"
        session_id = rpg_engine.start_new_game(user_id)
        
        assert session_id is not None
        assert len(session_id) > 0
        
        # Test with custom session ID
        custom_session_id = "custom_session_123"
        returned_session_id = rpg_engine.start_new_game(user_id, custom_session_id)
        assert returned_session_id == custom_session_id
    
    def test_process_user_input(self, rpg_engine):
        """Test processing user input."""
        user_id = "test_user"
        session_id = rpg_engine.start_new_game(user_id)
        
        user_input = "Hello, I want to create a character named Alice."
        response = rpg_engine.process_user_input(session_id, user_input)
        
        assert response is not None
        assert len(response) > 0
        assert isinstance(response, str)
    
    def test_get_game_state(self, rpg_engine):
        """Test getting game state."""
        user_id = "test_user"
        session_id = rpg_engine.start_new_game(user_id)
        
        # Process some input first
        rpg_engine.process_user_input(session_id, "Create character")
        
        state = rpg_engine.get_game_state(session_id)
        assert state is not None
        assert isinstance(state, dict)
    
    def test_list_active_sessions(self, rpg_engine):
        """Test listing active sessions."""
        user_id = "test_user"
        
        # Start multiple sessions
        session1 = rpg_engine.start_new_game(user_id)
        session2 = rpg_engine.start_new_game(user_id)
        
        sessions = rpg_engine.list_active_sessions(user_id)
        assert isinstance(sessions, list)


class TestGameState:
    """Test cases for game state management."""
    
    def test_create_initial_state(self):
        """Test creating initial game state."""
        session_id = "test_session"
        user_id = "test_user"
        
        state = create_initial_state(session_id, user_id)
        
        assert state["session_id"] == session_id
        assert state["user_id"] == user_id
        assert state["game_phase"] == GamePhase.INITIALIZATION
        assert state["action_type"] == ActionType.CHARACTER_CREATION
        assert state["player_character"] is None
        assert state["messages"] == []
        assert state["retry_count"] == 0
    
    def test_state_structure(self):
        """Test that game state has all required fields."""
        state = create_initial_state("test", "test")
        
        required_fields = [
            "messages", "session_id", "user_id", "game_phase", "action_type",
            "player_character", "world_context", "story_context",
            "conversation_summary", "context_window_used", "max_context_window",
            "needs_memory_retrieval", "needs_world_update", "needs_story_validation",
            "content_approved", "generated_response", "editor_feedback",
            "world_updates", "error_message", "retry_count"
        ]
        
        for field in required_fields:
            assert field in state, f"Missing required field: {field}"


class TestIntegration:
    """Integration tests for the complete system."""
    
    def test_character_creation_flow(self, rpg_engine):
        """Test the complete character creation flow."""
        user_id = "test_user"
        session_id = rpg_engine.start_new_game(user_id)
        
        # Start character creation
        response1 = rpg_engine.process_user_input(
            session_id, 
            "I want to create a character named Bob, a brave warrior."
        )
        assert "Bob" in response1 or "character" in response1.lower()
        
        # Continue with character details
        response2 = rpg_engine.process_user_input(
            session_id,
            "Bob is tall and strong, with a noble background."
        )
        assert len(response2) > 0
    
    def test_world_interaction_flow(self, rpg_engine):
        """Test world interaction flow."""
        user_id = "test_user"
        session_id = rpg_engine.start_new_game(user_id)
        
        # Create character first
        rpg_engine.process_user_input(session_id, "Create character named Alice")
        
        # Explore the world
        response = rpg_engine.process_user_input(
            session_id,
            "Look around and explore the area."
        )
        assert len(response) > 0
        
        # Perform an action
        response = rpg_engine.process_user_input(
            session_id,
            "Walk to the nearby forest."
        )
        assert len(response) > 0
    
    def test_story_progression(self, rpg_engine):
        """Test story progression over multiple interactions."""
        user_id = "test_user"
        session_id = rpg_engine.start_new_game(user_id)
        
        interactions = [
            "Create a character named Charlie, a clever rogue.",
            "Look around the starting village.",
            "Talk to the local innkeeper.",
            "Ask about any interesting rumors or quests.",
            "Accept the quest and prepare to leave."
        ]
        
        responses = []
        for interaction in interactions:
            response = rpg_engine.process_user_input(session_id, interaction)
            responses.append(response)
            assert len(response) > 0
        
        # Verify story progression
        assert len(responses) == len(interactions)
        
        # Check that responses are contextually related
        # (This is a simple check - in practice you'd want more sophisticated validation)
        combined_text = " ".join(responses).lower()
        assert "charlie" in combined_text or "character" in combined_text


@pytest.mark.asyncio
class TestErrorHandling:
    """Test error handling scenarios."""
    
    def test_invalid_session_id(self, rpg_engine):
        """Test handling of invalid session ID."""
        invalid_session = "nonexistent_session"
        
        # This should handle gracefully
        response = rpg_engine.process_user_input(invalid_session, "Hello")
        assert response is not None  # Should return some error message
    
    def test_empty_input(self, rpg_engine):
        """Test handling of empty input."""
        user_id = "test_user"
        session_id = rpg_engine.start_new_game(user_id)
        
        response = rpg_engine.process_user_input(session_id, "")
        assert response is not None
        assert len(response) > 0
    
    def test_very_long_input(self, rpg_engine):
        """Test handling of very long input."""
        user_id = "test_user"
        session_id = rpg_engine.start_new_game(user_id)
        
        long_input = "A" * 10000  # Very long input
        response = rpg_engine.process_user_input(session_id, long_input)
        assert response is not None
        assert len(response) > 0


if __name__ == "__main__":
    pytest.main([__file__])
