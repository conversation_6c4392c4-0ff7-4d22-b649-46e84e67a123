# RPG Engine Makefile

.PHONY: help install install-dev test test-cov lint format clean run-api run-cli init-db docs

# Default target
help:
	@echo "RPG Engine - Collaborative Story Creation Engine"
	@echo ""
	@echo "Available commands:"
	@echo "  install      Install the package and dependencies"
	@echo "  install-dev  Install with development dependencies"
	@echo "  test         Run tests"
	@echo "  test-cov     Run tests with coverage"
	@echo "  lint         Run linting checks"
	@echo "  format       Format code with black and isort"
	@echo "  clean        Clean up build artifacts"
	@echo "  run-api      Start the API server"
	@echo "  run-cli      Start interactive CLI session"
	@echo "  init-db      Initialize the database"
	@echo "  docs         Generate documentation"

# Installation
install:
	pip install -e .

install-dev:
	pip install -e ".[dev,api,cli]"

# Testing
test:
	pytest tests/ -v

test-cov:
	pytest tests/ --cov=rpg_engine --cov-report=html --cov-report=term

# Code quality
lint:
	flake8 rpg_engine tests
	mypy rpg_engine
	black --check rpg_engine tests
	isort --check-only rpg_engine tests

format:
	black rpg_engine tests
	isort rpg_engine tests

# Cleanup
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

# Running
run-api:
	python -m rpg_engine.cli.main server --reload

run-cli:
	.venv/bin/python -m rpg_engine.cli.main play

init-db:
	.venv/bin/python -m rpg_engine.cli.main init-db

# Development
dev-setup: install-dev init-db
	@echo "Development environment set up!"
	@echo "Copy .env.example to .env and configure your API keys"

# Docker commands (if using Docker)
docker-build:
	docker build -t rpg-engine .

docker-run:
	docker run -p 8000:8000 rpg-engine

# Documentation
docs:
	@echo "Documentation generation not yet implemented"
	@echo "See README.md for current documentation"

# Release
build:
	python setup.py sdist bdist_wheel

upload-test:
	twine upload --repository testpypi dist/*

upload:
	twine upload dist/*

# Database management
reset-db:
	python -m rpg_engine.cli.main init-db --reset

backup-db:
	@echo "Database backup not yet implemented"

# Monitoring and health checks
health-check:
	curl -f http://localhost:8000/health || exit 1

# Load testing (requires additional tools)
load-test:
	@echo "Load testing not yet implemented"
	@echo "Consider using tools like locust or artillery"
