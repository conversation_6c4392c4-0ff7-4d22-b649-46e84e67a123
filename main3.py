import os
import logging
from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI
from langgraph.store.memory import InMemoryStore
from langgraph.config import get_store
from langmem import (
    # Lets agent create, update, and delete memories
    create_manage_memory_tool,
)

from dotenv import load_dotenv
import asyncio
from langchain_huggingface import HuggingFaceEmbeddings
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
from pydantic import SecretStr

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

load_dotenv()

def prompt(state):
    """Prepare the messages for the LLM."""
    # Get store from configured contextvar;
    store = get_store() # Same as that provided to `create_react_agent`
    memories = store.search(
        # Search within the same namespace as the one
        # we've configured for the agent
        ("memories",),
        query=state["messages"][-1].content,
    )
    system_msg = f"""You are a helpful assistant.

## Memories
<memories>
{memories}
</memories>
"""
    return [{"role": "system", "content": system_msg}, *state["messages"]]

async def main():
    llm = ChatOpenAI(
        model="deepseek/deepseek-chat-v3-0324:free",
        base_url="https://openrouter.ai/api/v1",
        api_key=os.getenv("OPENROUTER_API_KEY"), # type: ignore
        streaming=False
    )

    from langgraph.store.base import IndexConfig  # Add this import at the top if not already present

    store = InMemoryStore(
        index=IndexConfig(  # Use IndexConfig instead of raw dict
            dims=768,
            embed=HuggingFaceEmbeddings(model_name="sentence-transformers/all-mpnet-base-v2").embed_documents, # type: ignore
        )
    )
    async with AsyncSqliteSaver.from_conn_string("db.db") as checkpointer:

        agent = create_react_agent(
            model=llm,
            prompt=prompt,
            tools=[ # Add memory tools
                # The agent can call "manage_memory" to
                # create, update, and delete memories by ID
                # Namespaces add scope to memories. To
                # scope memories per-user, do ("memories", "{user_id}"):
                create_manage_memory_tool(namespace=("memories",)),
            ],
            # Our memories will be stored in this provided BaseStore instance
            store=store,
            # And the graph "state" will be checkpointed after each node
            # completes executing for tracking the chat history and durable execution
            checkpointer=checkpointer,
        )

        # Get properly structured config from checkpointer
        # Create config manually with thread_id
        config = {"configurable": {"thread_id": 1}}

        # Use the agent. The agent hasn't saved any memories,
        # so it doesn't know about us
        response = await agent.ainvoke(
            {
                "messages": [
                    {"role": "user", "content": "Know which display mode I prefer?"}
                ]
            },
            config=config, # type: ignore
        )
        print(response["messages"][-1].content)

        await agent.ainvoke(
            {
                "messages": [
                    {"role": "user", "content": "dark. Remember that."}
                ]
            },
            # We will continue the conversation (thread-a) by using the config with
            # the same thread_id
            config=config, # type: ignore
        )

        # The agent will only be able to recall
        # whatever it explicitly saved using the manage_memories tool
        response = await agent.ainvoke(
            {"messages": [{"role": "user", "content": "Hey there. Do you remember me? What are my preferences?"}]},
            config=config, # type: ignore
        )
        print(response["messages"][-1].content)


if __name__ == "__main__":
    asyncio.run(main())