# RPG Engine - Collaborative Story Creation

A sophisticated role-playing game engine built with LangGraph that creates immersive, collaborative storytelling experiences. The AI provides dynamic characters and builds rich worlds while users create and play their characters in an interactive narrative environment.

## 🎮 Features

### Core Functionality
- **Dynamic Character Creation**: AI-assisted character generation with personality traits, skills, and backgrounds
- **Intelligent World Building**: Procedurally generated locations, events, and NPCs with rich descriptions
- **Story Editor Agent**: AI agent that maintains narrative consistency, tone, and quality
- **Long-term Memory**: Conversation summarization and context management for extended gameplay
- **Persistent State**: SQLite database storage for characters, world state, and conversation history

### Technical Highlights
- **LangGraph Framework**: State machine-based workflow for complex AI interactions
- **Modular Architecture**: Object-oriented design with clear separation of concerns
- **Memory Management**: Intelligent context window management and conversation summarization
- **Relationship Tracking**: Dynamic character relationships and interaction history
- **Event System**: Background events and world state changes over time

## 🏗️ Architecture

The system is built around a LangGraph state machine with the following key components:

```mermaid
graph TD
    A[User Input] --> B[Input Processor]
    B --> C{Action Type?}
    
    C -->|Character Action| D[Character Action Node]
    C -->|World Query| E[World Query Node]
    C -->|Story Continue| F[Story Generation Node]
    C -->|Character Creation| G[Character Creation Node]
    
    D --> H[Memory Retrieval]
    E --> H
    F --> H
    G --> H
    
    H --> I[Context Assembly]
    I --> J[LLM Processing]
    J --> K[Story Editor Agent]
    
    K --> L{Content Valid?}
    L -->|No| M[Content Revision]
    M --> J
    L -->|Yes| N[World State Update]
    
    N --> O[Memory Storage]
    O --> P[Response Generation]
    P --> Q[User Output]
```

### Core Components

1. **Game Engine** (`rpg_engine/core/game_engine.py`)
   - Main orchestration using LangGraph
   - State management and workflow coordination
   - Session handling and persistence

2. **Memory Manager** (`rpg_engine/memory/manager.py`)
   - Conversation summarization
   - Long-term memory storage and retrieval
   - Semantic search with embeddings

3. **Character Manager** (`rpg_engine/characters/manager.py`)
   - Player and NPC creation
   - Personality modeling and relationship tracking
   - Character development and progression

4. **World Engine** (`rpg_engine/world/engine.py`)
   - Dynamic location generation
   - Background event processing
   - World state management

5. **Story Editor Agent** (`rpg_engine/agents/story_editor.py`)
   - Content validation and consistency checking
   - Narrative tone maintenance
   - Quality assurance

## 🚀 Quick Start

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd tandem-llm
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
export OPENAI_API_KEY="your-openai-api-key"
# OR
export ANTHROPIC_API_KEY="your-anthropic-api-key"
export LLM_PROVIDER="openai"  # or "anthropic"
```

### Initialize Database

```bash
python -m rpg_engine.cli.main init-db
```

### Start Interactive Session

```bash
python -m rpg_engine.cli.main play
```

### Start API Server

```bash
python -m rpg_engine.cli.main server
```

The API will be available at `http://localhost:8000` with documentation at `http://localhost:8000/docs`.

## 📖 Usage Examples

### CLI Interactive Mode

```bash
# Start a new game
python -m rpg_engine.cli.main play --user-id "player1"

# Example interaction:
You: I want to create a character named Aria, a skilled archer from the forest.
Game Master: Excellent! Aria sounds like a fascinating character. Tell me more about her background...

You: She grew up in the Silverleaf Forest, learning archery from her elven mentor.
Game Master: Aria's connection to the Silverleaf Forest shapes her worldview...

You: I want to explore the nearby village.
Game Master: As Aria approaches the village of Millbrook, she notices...
```

### API Usage

```python
import requests

# Start a new game
response = requests.post("http://localhost:8000/game/start", json={
    "user_id": "player1"
})
session_id = response.json()["session_id"]

# Send user input
response = requests.post("http://localhost:8000/game/input", json={
    "session_id": session_id,
    "user_input": "Create a character named Bob, a brave knight."
})
print(response.json()["response"])

# Get game state
response = requests.get(f"http://localhost:8000/game/state/{session_id}")
print(response.json())
```

## 🗄️ Database Schema

The system uses SQLite with the following main tables:

- **worlds**: Location data, world state, and environmental information
- **characters**: Player and NPC data with personality traits and relationships
- **events**: World events, both active and historical
- **conversations**: Message history and conversation metadata
- **memories**: Long-term memory storage with importance scoring

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_game_engine.py

# Run with coverage
pytest --cov=rpg_engine tests/
```

## 🔧 Configuration

### Environment Variables

- `LLM_PROVIDER`: "openai" or "anthropic"
- `OPENAI_API_KEY`: OpenAI API key
- `ANTHROPIC_API_KEY`: Anthropic API key
- `DATABASE_URL`: Database connection string (default: sqlite:///rpg_game.db)
- `CHECKPOINTER_PATH`: LangGraph checkpointer database path

### Memory Management

- `max_conversation_length`: Maximum messages before summarization (default: 50)
- `memory_importance_threshold`: Minimum importance for long-term storage (default: 3.0)
- `max_context_window`: Maximum context window size (default: 4000)

## 🎯 Advanced Features

### Custom Character Creation

```python
from rpg_engine.characters.manager import CharacterManager

character_manager = CharacterManager(llm)
character = character_manager.create_player_character(
    session_id="session123",
    name="Thorin",
    background="dwarf_warrior",
    personality_traits=["brave", "stubborn", "loyal"],
    custom_skills={"combat": 8, "crafting": 6, "diplomacy": 3}
)
```

### World Generation

```python
from rpg_engine.world.engine import WorldEngine

world_engine = WorldEngine(llm)
world_context = world_engine.generate_world(
    session_id="session123",
    world_name="Realm of Shadows"
)
```

### Memory Retrieval

```python
from rpg_engine.memory.manager import MemoryManager

memory_manager = MemoryManager(llm)
memories = memory_manager.retrieve_memories(
    session_id="session123",
    query="dragon encounter",
    memory_types=["event", "character"],
    limit=5
)
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [LangGraph](https://github.com/langchain-ai/langgraph) for state machine orchestration
- Uses [LangChain](https://github.com/langchain-ai/langchain) for LLM integration
- Database management with [SQLAlchemy](https://sqlalchemy.org/)
- API framework with [FastAPI](https://fastapi.tiangolo.com/)
- CLI interface with [Typer](https://typer.tiangolo.com/) and [Rich](https://rich.readthedocs.io/)

## 🔮 Future Enhancements

- Multi-player support with shared worlds
- Voice interface integration
- Advanced AI personality models
- Plugin system for custom content
- Real-time collaboration features
- Enhanced visualization and mapping
- Mobile app interface
