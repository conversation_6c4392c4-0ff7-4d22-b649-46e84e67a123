# RPG Engine Environment Configuration

# LLM Provider Configuration
LLM_PROVIDER=openai  # Options: openai, anthropic

# OpenAI Configuration (if using OpenAI)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4  # Options: gpt-4, gpt-3.5-turbo

# Anthropic Configuration (if using Anthropic)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229  # Options: claude-3-sonnet-20240229, claude-3-haiku-20240307

# Database Configuration
DATABASE_URL=sqlite:///rpg_game.db  # SQLite database path
# For PostgreSQL: postgresql://user:password@localhost/rpg_game
# For MySQL: mysql://user:password@localhost/rpg_game

# LangGraph Checkpointer Configuration
CHECKPOINTER_PATH=rpg_checkpoints.db

# API Server Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=false

# Logging Configuration
LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR

# Memory Management Configuration
MAX_CONVERSATION_LENGTH=50
MEMORY_IMPORTANCE_THRESHOLD=3.0
MAX_CONTEXT_WINDOW=4000

# Development Configuration
DEBUG=false
TESTING=false
