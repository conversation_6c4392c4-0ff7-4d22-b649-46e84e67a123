import os
import logging
from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI
from langgraph.store.memory import InMemoryStore
from langmem import create_manage_memory_tool, create_search_memory_tool
from langgraph.store.base import IndexConfig

from dotenv import load_dotenv
import asyncio
from langchain_huggingface import HuggingFaceEmbeddings
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
from pydantic import SecretStr

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

load_dotenv()

model = ChatOpenAI(
    model="deepseek/deepseek-chat-v3-0324:free",
    base_url="https://openrouter.ai/api/v1",
    api_key=os.getenv("OPENROUTER_API_KEY"), # type: ignore
    streaming=False
)

store = InMemoryStore(
    index=IndexConfig(
        dims=768,
        embed=HuggingFaceEmbeddings(model_name="sentence-transformers/all-mpnet-base-v2").embed_documents, # type: ignore
    )
)

async def main():

    async with AsyncSqliteSaver.from_conn_string("db.db") as checkpointer:

        agent = create_react_agent(
            model=model,
            tools=[
                create_manage_memory_tool(namespace=("memories", "{user_id}")),
                create_search_memory_tool(namespace=("memories", "{user_id}")),
            ],
            store=store,
            # And the graph "state" will be checkpointed after each node
            # completes executing for tracking the chat history and durable execution
            checkpointer=checkpointer,
        )

        # Get properly structured config from checkpointer
        # Create config manually with user_id
        config = {"configurable": {"user_id": "user", "thread_id": "thread"}}

        # Store a preference
        response = await agent.ainvoke(
            {"messages": [{"role": "user", "content": "Remember I prefer dark mode"}]},
            config=config,
        )
        print("=" * 20, response["messages"][-1].content)

        # Search preferences
        response = await agent.ainvoke(
            {"messages": [{"role": "user", "content": "What are my preferences?"}]},
            config=config,
        )
        print("=" * 20, response["messages"][-1].content)

if __name__ == "__main__":
    asyncio.run(main())