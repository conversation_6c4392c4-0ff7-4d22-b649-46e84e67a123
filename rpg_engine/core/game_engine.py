"""
Main RPG game engine built with LangGraph.

This module contains the core game engine that orchestrates the entire
collaborative story creation workflow using LangGraph's state machine.
"""

import logging
from typing import Dict, Any, Optional
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import HumanMessage

from langgraph.graph import State<PERSON>raph, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.checkpoint.sqlite import SqliteSaver

from .state import GameState, ActionType, GamePhase, create_initial_state
from .nodes import RPGNodes
from ..memory.manager import MemoryManager
from ..agents.story_editor import StoryEditorAgent
from ..database import get_database_manager, init_database

logger = logging.getLogger(__name__)


class RPGEngine:
    """
    Main RPG engine that orchestrates the collaborative story creation process.
    
    This engine uses LangGraph to manage the complex workflow of character creation,
    world building, story generation, and narrative consistency checking.
    """
    
    def __init__(
        self, 
        llm: BaseChatModel,
        database_url: str = None,
        checkpointer_path: str = "rpg_checkpoints.db"
    ):
        """
        Initialize the RPG engine.
        
        Args:
            llm: Language model for story generation
            database_url: Database URL for persistent storage
            checkpointer_path: Path for LangGraph checkpointer database
        """
        self.llm = llm
        
        # Initialize database
        init_database(database_url)
        self.db_manager = get_database_manager()
        
        # Initialize components
        self.memory_manager = MemoryManager(llm)
        self.story_editor = StoryEditorAgent(llm)
        self.nodes = RPGNodes(llm, self.memory_manager, self.story_editor)
        
        # Initialize checkpointer for conversation state
        self.checkpointer = SqliteSaver.from_conn_string(f"sqlite:///{checkpointer_path}")
        
        # Build the LangGraph workflow
        self.graph = self._build_graph()
        
        logger.info("RPG Engine initialized successfully")
    
    def _build_graph(self) -> StateGraph:
        """
        Build the LangGraph workflow for the RPG engine.
        
        Returns:
            StateGraph: Compiled LangGraph workflow
        """
        # Create the state graph
        workflow = StateGraph(GameState)
        
        # Add nodes
        workflow.add_node("input_processor", self.nodes.input_processor)
        workflow.add_node("character_creation", self.nodes.character_creation_node)
        workflow.add_node("character_action", self.nodes.character_action_node)
        workflow.add_node("world_query", self.nodes.world_query_node)
        workflow.add_node("story_generation", self.nodes.story_generation_node)
        workflow.add_node("memory_retrieval", self.nodes.memory_retrieval_node)
        workflow.add_node("world_update", self.nodes.world_update_node)
        workflow.add_node("story_editor", self.story_editor.validate_content)
        workflow.add_node("response_generation", self._response_generation_node)
        
        # Set entry point
        workflow.set_entry_point("input_processor")
        
        # Add conditional edges based on action type
        workflow.add_conditional_edges(
            "input_processor",
            self._route_action,
            {
                "character_creation": "character_creation",
                "character_action": "character_action", 
                "world_query": "world_query",
                "story_continue": "story_generation",
                "memory_retrieval": "memory_retrieval"
            }
        )
        
        # Connect action nodes to memory retrieval
        workflow.add_edge("character_creation", "memory_retrieval")
        workflow.add_edge("character_action", "memory_retrieval")
        workflow.add_edge("world_query", "memory_retrieval")
        workflow.add_edge("story_generation", "memory_retrieval")
        
        # Memory retrieval leads to story editor
        workflow.add_edge("memory_retrieval", "story_editor")
        
        # Story editor conditional routing
        workflow.add_conditional_edges(
            "story_editor",
            self._route_editor_decision,
            {
                "approved": "world_update",
                "revision_needed": "story_generation"
            }
        )
        
        # World update leads to response generation
        workflow.add_edge("world_update", "response_generation")
        
        # Response generation ends the workflow
        workflow.add_edge("response_generation", END)
        
        # Compile the graph with checkpointer
        return workflow.compile(checkpointer=self.checkpointer)
    
    def _route_action(self, state: GameState) -> str:
        """
        Route to appropriate node based on action type.
        
        Args:
            state: Current game state
            
        Returns:
            str: Next node name
        """
        action_type = state.get("action_type", ActionType.CHARACTER_CREATION)
        
        if action_type == ActionType.CHARACTER_CREATION:
            return "character_creation"
        elif action_type == ActionType.CHARACTER_ACTION:
            return "character_action"
        elif action_type == ActionType.WORLD_QUERY:
            return "world_query"
        elif action_type == ActionType.STORY_CONTINUE:
            return "story_continue"
        else:
            return "memory_retrieval"
    
    def _route_editor_decision(self, state: GameState) -> str:
        """
        Route based on story editor's decision.
        
        Args:
            state: Current game state
            
        Returns:
            str: Next node name
        """
        if state.get("content_approved", False):
            return "approved"
        else:
            return "revision_needed"
    
    def _response_generation_node(self, state: GameState) -> GameState:
        """
        Final response generation and cleanup.
        
        Args:
            state: Current game state
            
        Returns:
            GameState: Updated state with final response
        """
        logger.info("Generating final response")
        
        # Save conversation to database
        self._save_conversation(state)
        
        # Update memory if needed
        if len(state["messages"]) % 10 == 0:  # Every 10 messages
            self.memory_manager.update_long_term_memory(state)
        
        # Reset processing flags
        state["needs_memory_retrieval"] = False
        state["needs_world_update"] = False
        state["needs_story_validation"] = False
        state["content_approved"] = False
        state["retry_count"] = 0
        
        return state
    
    def _save_conversation(self, state: GameState):
        """Save conversation to database."""
        if not state["messages"]:
            return
            
        with self.db_manager.get_session() as session:
            from ..database.models import Conversation
            
            last_message = state["messages"][-1]
            
            conversation = Conversation(
                session_id=state["session_id"],
                thread_id=state.get("thread_id", state["session_id"]),
                message_type="assistant",
                content=last_message.content,
                action_type=state["action_type"].value,
                game_phase=state["game_phase"].value,
                world_id=1  # Default world ID
            )
            
            session.add(conversation)
    
    def start_new_game(self, user_id: str, session_id: str = None) -> str:
        """
        Start a new RPG game session.
        
        Args:
            user_id: Unique user identifier
            session_id: Optional session ID (generated if not provided)
            
        Returns:
            str: Session ID for the new game
        """
        if session_id is None:
            import uuid
            session_id = str(uuid.uuid4())
        
        # Create initial state
        initial_state = create_initial_state(session_id, user_id)
        
        # Configure for LangGraph
        config = {"configurable": {"thread_id": session_id}}
        
        # Start the game with initial prompt
        initial_input = {"messages": []}
        
        # Run the graph to generate welcome message
        result = self.graph.invoke(initial_state, config)
        
        logger.info(f"Started new game session: {session_id}")
        return session_id
    
    def process_user_input(self, session_id: str, user_input: str) -> str:
        """
        Process user input and generate response.
        
        Args:
            session_id: Game session ID
            user_input: User's input message
            
        Returns:
            str: Generated response
        """
        config = {"configurable": {"thread_id": session_id}}
        
        # Add user message to state
        input_state = {
            "messages": [HumanMessage(content=user_input)]
        }
        
        try:
            # Process through the graph
            result = self.graph.invoke(input_state, config)
            
            # Extract the response
            if result["messages"]:
                response = result["messages"][-1].content
            else:
                response = result.get("generated_response", "I'm not sure how to respond to that.")
            
            logger.info(f"Processed input for session {session_id}")
            return response
            
        except Exception as e:
            logger.error(f"Error processing user input: {e}")
            return "I encountered an error processing your request. Please try again."
    
    def get_game_state(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get current game state for a session.
        
        Args:
            session_id: Game session ID
            
        Returns:
            Dict containing current game state or None if not found
        """
        config = {"configurable": {"thread_id": session_id}}
        
        try:
            snapshot = self.graph.get_state(config)
            return snapshot.values if snapshot else None
        except Exception as e:
            logger.error(f"Error getting game state: {e}")
            return None
    
    def list_active_sessions(self, user_id: str) -> List[str]:
        """
        List active game sessions for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of active session IDs
        """
        with self.db_manager.get_session() as session:
            from ..database.models import World
            
            worlds = session.query(World).filter(
                World.session_id.like(f"{user_id}%")
            ).all()
            
            return [world.session_id for world in worlds]
