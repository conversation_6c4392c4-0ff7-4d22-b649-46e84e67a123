"""
LangGraph nodes for the RPG engine.

This module contains all the node functions that make up the LangGraph workflow
for the collaborative story creation engine.
"""

import logging
from typing import Dict, Any, List
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.language_models import BaseChatModel

from .state import GameState, ActionType, GamePhase, PlayerCharacter, WorldContext
from ..database import get_database_manager, World, Character, Event, Conversation
from ..memory.manager import MemoryManager
from ..agents.story_editor import StoryEditorAgent

logger = logging.getLogger(__name__)


class RPGNodes:
    """
    Collection of LangGraph nodes for the RPG engine.
    """

    def __init__(self, llm: BaseChatModel, memory_manager: MemoryManager, story_editor: StoryEditorAgent):
        self.llm = llm
        self.memory_manager = memory_manager
        self.story_editor = story_editor
        self.db_manager = get_database_manager()

    def input_processor(self, state: GameState) -> GameState:
        """
        Process user input and determine the action type.
        """
        logger.info("Processing user input")

        if not state["messages"]:
            state["action_type"] = ActionType.CHARACTER_CREATION
            return state

        last_message = state["messages"][-1]
        if isinstance(last_message, HumanMessage):
            content = last_message.content.lower()

            # Determine action type based on content
            if any(word in content for word in ["create", "character", "new"]):
                state["action_type"] = ActionType.CHARACTER_CREATION
            elif any(word in content for word in ["explore", "look", "examine", "where"]):
                state["action_type"] = ActionType.WORLD_QUERY
            elif any(word in content for word in ["continue", "next", "story", "what happens"]):
                state["action_type"] = ActionType.STORY_CONTINUE
            else:
                state["action_type"] = ActionType.CHARACTER_ACTION

        state["needs_memory_retrieval"] = True
        return state

    def character_creation_node(self, state: GameState) -> GameState:
        """
        Handle character creation process.
        """
        logger.info("Processing character creation")

        if not state["messages"]:
            # Initial character creation prompt
            system_prompt = """You are a helpful RPG character creation assistant.
            Help the user create their character by asking about their name, background,
            personality traits, and skills. Be engaging and creative."""

            response = "Welcome to the RPG world! Let's create your character. What's your character's name and what kind of person are they?"

            state["messages"].append(AIMessage(content=response))
            state["generated_response"] = response
            state["game_phase"] = GamePhase.CHARACTER_CREATION
            return state

        # Process character creation input
        last_message = state["messages"][-1]
        if isinstance(last_message, HumanMessage):
            # Use LLM to extract character information
            extraction_prompt = f"""
            Extract character information from this user input: "{last_message.content}"

            Return a JSON object with these fields:
            - name: character name
            - description: physical description
            - background: character background/history
            - personality_traits: list of personality traits
            - skills: dict of skill names and levels (1-10)

            If information is missing, ask follow-up questions.
            """

            response = self.llm.invoke([SystemMessage(content=extraction_prompt)])

            # For now, create a basic character (in production, parse the LLM response)
            if "name" in last_message.content.lower():
                character = PlayerCharacter(
                    name="Player Character",
                    description="A brave adventurer",
                    background="Unknown origins",
                    personality_traits=["brave", "curious"],
                    skills={"combat": 5, "magic": 3, "stealth": 4},
                    inventory=["basic sword", "health potion"],
                    current_location="starting_village"
                )

                state["player_character"] = character
                state["game_phase"] = GamePhase.WORLD_BUILDING

                response_text = f"Great! Your character {character.name} has been created. You find yourself in a small village at the edge of a vast wilderness. What would you like to do?"
            else:
                response_text = "Please tell me your character's name and a bit about their background."

            state["messages"].append(AIMessage(content=response_text))
            state["generated_response"] = response_text

        return state

    def character_action_node(self, state: GameState) -> GameState:
        """
        Process character actions in the world.
        """
        logger.info("Processing character action")

        if not state["player_character"]:
            state["error_message"] = "No character found. Please create a character first."
            return state

        last_message = state["messages"][-1]
        if isinstance(last_message, HumanMessage):
            # Create context for the LLM
            context = self._build_action_context(state)

            action_prompt = f"""
            You are the game master for an RPG. The player character {state['player_character'].name}
            wants to: {last_message.content}

            Current context:
            {context}

            Describe what happens as a result of this action. Be creative, engaging, and consistent
            with the world. Include any consequences, discoveries, or new opportunities.
            """

            response = self.llm.invoke([SystemMessage(content=action_prompt)])

            state["messages"].append(AIMessage(content=response.content))
            state["generated_response"] = response.content
            state["needs_world_update"] = True
            state["needs_story_validation"] = True

        return state

    def world_query_node(self, state: GameState) -> GameState:
        """
        Handle queries about the world state.
        """
        logger.info("Processing world query")

        last_message = state["messages"][-1]
        if isinstance(last_message, HumanMessage):
            # Build world context
            world_context = self._build_world_context(state)

            query_prompt = f"""
            You are describing the world to a player. They asked: {last_message.content}

            Current world state:
            {world_context}

            Provide a detailed, immersive description that helps the player understand
            their surroundings and options.
            """

            response = self.llm.invoke([SystemMessage(content=query_prompt)])

            state["messages"].append(AIMessage(content=response.content))
            state["generated_response"] = response.content

        return state

    def story_generation_node(self, state: GameState) -> GameState:
        """
        Generate story content and narrative progression.
        """
        logger.info("Generating story content")

        # Build comprehensive context
        story_context = self._build_story_context(state)

        story_prompt = f"""
        You are a creative storyteller and game master. Continue the story based on recent events.

        Story context:
        {story_context}

        Generate engaging narrative that:
        1. Advances the plot
        2. Introduces new elements or challenges
        3. Maintains consistency with established world and characters
        4. Provides clear options for player action
        """

        response = self.llm.invoke([SystemMessage(content=story_prompt)])

        state["messages"].append(AIMessage(content=response.content))
        state["generated_response"] = response.content
        state["needs_story_validation"] = True

        return state

    def memory_retrieval_node(self, state: GameState) -> GameState:
        """
        Retrieve relevant memories and context.
        """
        logger.info("Retrieving memories")

        if state["needs_memory_retrieval"]:
            # Get recent conversation context
            recent_messages = state["messages"][-10:] if len(state["messages"]) > 10 else state["messages"]

            # Retrieve relevant memories
            query = " ".join([msg.content for msg in recent_messages if hasattr(msg, 'content')])
            memories = self.memory_manager.retrieve_memories(state["session_id"], query)

            # Update conversation summary if needed
            if len(state["messages"]) > state["max_context_window"] // 100:  # Rough estimate
                summary = self.memory_manager.summarize_conversation(state["messages"])
                state["conversation_summary"] = summary

            state["needs_memory_retrieval"] = False

        return state

    def world_update_node(self, state: GameState) -> GameState:
        """
        Update world state based on actions and events.
        """
        logger.info("Updating world state")

        if state["needs_world_update"]:
            # Update character location, inventory, etc.
            if state["player_character"]:
                # Save character state to database
                self._save_character_state(state)

            # Update world events and state
            self._update_world_events(state)

            state["needs_world_update"] = False

        return state

    def _build_action_context(self, state: GameState) -> str:
        """Build context string for character actions."""
        character = state["player_character"]
        world = state["world_context"]

        context = f"""
        Character: {character.name}
        Location: {character.current_location}
        Health: {character.health}
        Inventory: {', '.join(character.inventory)}

        World State:
        - Time: {world.time_of_day}
        - Weather: {world.weather}
        - Atmosphere: {world.atmosphere}
        - Available NPCs: {', '.join(world.active_npcs)}
        """

        return context

    def _build_world_context(self, state: GameState) -> str:
        """Build context string for world queries."""
        world = state["world_context"]

        context = f"""
        Current Location: {world.current_location}
        Available Locations: {', '.join(world.available_locations)}
        Time of Day: {world.time_of_day}
        Weather: {world.weather}
        Active Events: {', '.join(world.current_events)}
        NPCs Present: {', '.join(world.active_npcs)}
        """

        return context

    def _build_story_context(self, state: GameState) -> str:
        """Build context string for story generation."""
        story = state["story_context"]

        context = f"""
        Current Scene: {story.current_scene}
        Story Summary: {story.story_summary}
        Recent Events: {', '.join(story.recent_events)}
        Narrative Tone: {story.narrative_tone}
        Active Plot Threads: {', '.join(story.plot_threads)}
        """

        return context

    def _save_character_state(self, state: GameState):
        """Save character state to database."""
        if not state["player_character"]:
            return

        with self.db_manager.get_session() as session:
            # Find or create character record
            character_record = session.query(Character).filter_by(
                session_id=state["session_id"],
                character_type="player"
            ).first()

            if not character_record:
                character_record = Character(
                    session_id=state["session_id"],
                    character_type="player",
                    world_id=1  # Default world ID
                )
                session.add(character_record)

            # Update character data
            char = state["player_character"]
            character_record.name = char.name
            character_record.description = char.description
            character_record.background = char.background
            character_record.personality_traits = char.personality_traits
            character_record.skills = char.skills
            character_record.inventory = char.inventory
            character_record.current_location = char.current_location
            character_record.health = char.health
            character_record.experience = char.experience
            character_record.relationships = char.relationships

    def _update_world_events(self, state: GameState):
        """Update world events and background processes."""
        with self.db_manager.get_session() as session:
            # Find or create world record
            world_record = session.query(World).filter_by(
                session_id=state["session_id"]
            ).first()

            if not world_record:
                world_record = World(
                    session_id=state["session_id"],
                    name="Game World"
                )
                session.add(world_record)

            # Update world state
            world = state["world_context"]
            world_record.current_location = world.current_location
            world_record.available_locations = world.available_locations
            world_record.world_state = world.world_state
            world_record.time_of_day = world.time_of_day
            world_record.weather = world.weather
            world_record.atmosphere = world.atmosphere
            world_record.active_events = world.current_events
