"""
Core state management for the RPG engine.

This module defines the main state structure used throughout the LangGraph
workflow, including game state, character information, and world context.
"""

from typing import Annotated, Dict, List, Optional, Any
from typing_extensions import TypedDict
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages


class ActionType(Enum):
    """Types of actions a user can perform."""
    CHARACTER_ACTION = "character_action"
    WORLD_QUERY = "world_query" 
    STORY_CONTINUE = "story_continue"
    CHARACTER_CREATION = "character_creation"
    WORLD_EXPLORATION = "world_exploration"


class GamePhase(Enum):
    """Current phase of the game."""
    INITIALIZATION = "initialization"
    CHARACTER_CREATION = "character_creation"
    WORLD_BUILDING = "world_building"
    ACTIVE_PLAY = "active_play"
    STORY_RESOLUTION = "story_resolution"


@dataclass
class PlayerCharacter:
    """Player character information."""
    name: str
    description: str
    background: str
    personality_traits: List[str]
    skills: Dict[str, int]
    inventory: List[str]
    current_location: str
    health: int = 100
    experience: int = 0
    relationships: Dict[str, str] = field(default_factory=dict)
    
    
@dataclass
class WorldContext:
    """Current world state and context."""
    current_location: str
    available_locations: List[str]
    active_npcs: List[str]
    current_events: List[str]
    world_state: Dict[str, Any]
    time_of_day: str
    weather: str
    atmosphere: str


@dataclass
class StoryContext:
    """Current story context and narrative state."""
    current_scene: str
    story_summary: str
    recent_events: List[str]
    narrative_tone: str
    story_themes: List[str]
    plot_threads: List[str]


class GameState(TypedDict):
    """
    Main state structure for the LangGraph workflow.
    
    This state is passed between all nodes in the graph and contains
    all necessary information for the RPG engine to function.
    """
    # Core conversation state
    messages: Annotated[List[BaseMessage], add_messages]
    
    # Game metadata
    session_id: str
    user_id: str
    game_phase: GamePhase
    action_type: ActionType
    
    # Character and world state
    player_character: Optional[PlayerCharacter]
    world_context: WorldContext
    story_context: StoryContext
    
    # Memory and context management
    conversation_summary: str
    context_window_used: int
    max_context_window: int
    
    # Processing flags
    needs_memory_retrieval: bool
    needs_world_update: bool
    needs_story_validation: bool
    content_approved: bool
    
    # Generated content
    generated_response: str
    editor_feedback: str
    world_updates: Dict[str, Any]
    
    # Error handling
    error_message: Optional[str]
    retry_count: int


def create_initial_state(session_id: str, user_id: str) -> GameState:
    """Create an initial game state for a new session."""
    return GameState(
        messages=[],
        session_id=session_id,
        user_id=user_id,
        game_phase=GamePhase.INITIALIZATION,
        action_type=ActionType.CHARACTER_CREATION,
        player_character=None,
        world_context=WorldContext(
            current_location="",
            available_locations=[],
            active_npcs=[],
            current_events=[],
            world_state={},
            time_of_day="morning",
            weather="clear",
            atmosphere="peaceful"
        ),
        story_context=StoryContext(
            current_scene="",
            story_summary="",
            recent_events=[],
            narrative_tone="adventurous",
            story_themes=[],
            plot_threads=[]
        ),
        conversation_summary="",
        context_window_used=0,
        max_context_window=4000,
        needs_memory_retrieval=False,
        needs_world_update=False,
        needs_story_validation=True,
        content_approved=False,
        generated_response="",
        editor_feedback="",
        world_updates={},
        error_message=None,
        retry_count=0
    )
