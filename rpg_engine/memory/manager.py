"""
Memory management system for the RPG engine.

This module handles conversation summarization, long-term memory storage,
and context window management to maintain coherent storytelling across
extended gameplay sessions.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage, SystemMessage, HumanMessage, AIMessage
from langchain_core.embeddings import Embeddings
from sentence_transformers import SentenceTransformer
import numpy as np
from sqlalchemy.orm import Session

from ..database import get_database_manager, Memory, Conversation
from ..core.state import GameState

logger = logging.getLogger(__name__)


class MemoryManager:
    """
    Manages conversation memory, summarization, and long-term storage.
    
    This class handles:
    - Conversation summarization when context window is exceeded
    - Long-term memory storage and retrieval
    - Semantic search for relevant memories
    - Context window management
    """
    
    def __init__(self, llm: BaseChatModel, embedding_model: str = "all-MiniLM-L6-v2"):
        """
        Initialize the memory manager.
        
        Args:
            llm: Language model for summarization
            embedding_model: Sentence transformer model for embeddings
        """
        self.llm = llm
        self.db_manager = get_database_manager()
        
        # Initialize embedding model for semantic search
        try:
            self.embedding_model = SentenceTransformer(embedding_model)
        except Exception as e:
            logger.warning(f"Could not load embedding model: {e}")
            self.embedding_model = None
        
        # Memory configuration
        self.max_conversation_length = 50  # Max messages before summarization
        self.summary_overlap = 10  # Messages to keep when summarizing
        self.memory_importance_threshold = 3.0  # Minimum importance for long-term storage
        
        logger.info("Memory manager initialized")
    
    def summarize_conversation(self, messages: List[BaseMessage]) -> str:
        """
        Summarize a conversation when it exceeds the context window.
        
        Args:
            messages: List of conversation messages
            
        Returns:
            str: Conversation summary
        """
        if len(messages) <= self.max_conversation_length:
            return ""
        
        # Extract conversation content
        conversation_text = self._messages_to_text(messages[:-self.summary_overlap])
        
        summary_prompt = f"""
        Summarize this RPG conversation, preserving key story elements:
        
        {conversation_text}
        
        Focus on:
        1. Important character actions and decisions
        2. World state changes and discoveries
        3. Plot developments and story progression
        4. Character relationships and interactions
        5. Significant events and their consequences
        
        Provide a concise but comprehensive summary that maintains story continuity.
        """
        
        try:
            response = self.llm.invoke([SystemMessage(content=summary_prompt)])
            summary = response.content
            
            logger.info(f"Generated conversation summary ({len(messages)} messages)")
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return "Previous conversation summary unavailable."
    
    def store_memory(
        self, 
        session_id: str, 
        memory_type: str, 
        title: str, 
        content: str,
        importance: float = 5.0,
        related_characters: List[str] = None,
        related_locations: List[str] = None,
        tags: List[str] = None
    ) -> bool:
        """
        Store a memory in long-term storage.
        
        Args:
            session_id: Game session ID
            memory_type: Type of memory (character, world, event, relationship)
            title: Memory title
            content: Memory content
            importance: Importance score (0-10)
            related_characters: List of related character names
            related_locations: List of related location names
            tags: List of tags for categorization
            
        Returns:
            bool: True if stored successfully
        """
        if importance < self.memory_importance_threshold:
            logger.debug(f"Memory importance too low ({importance}), not storing")
            return False
        
        try:
            with self.db_manager.get_session() as session:
                memory = Memory(
                    session_id=session_id,
                    memory_type=memory_type,
                    title=title,
                    content=content,
                    importance_score=importance,
                    related_characters=related_characters or [],
                    related_locations=related_locations or [],
                    tags=tags or []
                )
                
                session.add(memory)
                
            logger.info(f"Stored memory: {title}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing memory: {e}")
            return False
    
    def retrieve_memories(
        self, 
        session_id: str, 
        query: str, 
        memory_types: List[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant memories based on a query.
        
        Args:
            session_id: Game session ID
            query: Search query
            memory_types: Filter by memory types
            limit: Maximum number of memories to return
            
        Returns:
            List of relevant memories
        """
        try:
            with self.db_manager.get_session() as session:
                # Base query
                query_obj = session.query(Memory).filter(
                    Memory.session_id == session_id
                )
                
                # Filter by memory types if specified
                if memory_types:
                    query_obj = query_obj.filter(Memory.memory_type.in_(memory_types))
                
                # Get all memories for this session
                memories = query_obj.order_by(Memory.importance_score.desc()).all()
                
                if not memories:
                    return []
                
                # If we have an embedding model, use semantic search
                if self.embedding_model and query.strip():
                    return self._semantic_search(memories, query, limit)
                else:
                    # Fallback to keyword search
                    return self._keyword_search(memories, query, limit)
                    
        except Exception as e:
            logger.error(f"Error retrieving memories: {e}")
            return []
    
    def _semantic_search(self, memories: List[Memory], query: str, limit: int) -> List[Dict[str, Any]]:
        """
        Perform semantic search on memories using embeddings.
        
        Args:
            memories: List of memory objects
            query: Search query
            limit: Maximum results
            
        Returns:
            List of relevant memories with similarity scores
        """
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query])[0]
            
            # Generate embeddings for all memories
            memory_texts = [f"{mem.title} {mem.content}" for mem in memories]
            memory_embeddings = self.embedding_model.encode(memory_texts)
            
            # Calculate similarities
            similarities = np.dot(memory_embeddings, query_embedding)
            
            # Sort by similarity and importance
            scored_memories = []
            for i, memory in enumerate(memories):
                score = similarities[i] * (memory.importance_score / 10.0)  # Weight by importance
                scored_memories.append((memory, score))
            
            # Sort by combined score
            scored_memories.sort(key=lambda x: x[1], reverse=True)
            
            # Return top results
            results = []
            for memory, score in scored_memories[:limit]:
                # Update access count
                memory.access_count += 1
                memory.last_accessed = datetime.now()
                
                results.append({
                    "id": memory.id,
                    "type": memory.memory_type,
                    "title": memory.title,
                    "content": memory.content,
                    "importance": memory.importance_score,
                    "similarity": float(score),
                    "related_characters": memory.related_characters,
                    "related_locations": memory.related_locations,
                    "tags": memory.tags
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return self._keyword_search(memories, query, limit)
    
    def _keyword_search(self, memories: List[Memory], query: str, limit: int) -> List[Dict[str, Any]]:
        """
        Fallback keyword search for memories.
        
        Args:
            memories: List of memory objects
            query: Search query
            limit: Maximum results
            
        Returns:
            List of relevant memories
        """
        query_words = query.lower().split()
        scored_memories = []
        
        for memory in memories:
            # Calculate keyword match score
            text = f"{memory.title} {memory.content}".lower()
            score = sum(1 for word in query_words if word in text)
            
            # Weight by importance
            final_score = score * (memory.importance_score / 10.0)
            
            if score > 0:  # Only include memories with keyword matches
                scored_memories.append((memory, final_score))
        
        # Sort by score
        scored_memories.sort(key=lambda x: x[1], reverse=True)
        
        # Return top results
        results = []
        for memory, score in scored_memories[:limit]:
            # Update access count
            memory.access_count += 1
            memory.last_accessed = datetime.now()
            
            results.append({
                "id": memory.id,
                "type": memory.memory_type,
                "title": memory.title,
                "content": memory.content,
                "importance": memory.importance_score,
                "similarity": float(score),
                "related_characters": memory.related_characters,
                "related_locations": memory.related_locations,
                "tags": memory.tags
            })
        
        return results
    
    def update_long_term_memory(self, state: GameState):
        """
        Update long-term memory based on current game state.
        
        Args:
            state: Current game state
        """
        try:
            # Extract important information from recent messages
            recent_messages = state["messages"][-5:] if len(state["messages"]) >= 5 else state["messages"]
            
            if not recent_messages:
                return
            
            # Analyze messages for important information
            self._extract_and_store_memories(state, recent_messages)
            
        except Exception as e:
            logger.error(f"Error updating long-term memory: {e}")
    
    def _extract_and_store_memories(self, state: GameState, messages: List[BaseMessage]):
        """
        Extract and store important memories from messages.
        
        Args:
            state: Current game state
            messages: Recent messages to analyze
        """
        conversation_text = self._messages_to_text(messages)
        
        extraction_prompt = f"""
        Analyze this RPG conversation and extract important information for long-term memory:
        
        {conversation_text}
        
        Extract:
        1. Character developments or new traits
        2. World discoveries or changes
        3. Important events or plot developments
        4. New relationships or relationship changes
        5. Significant items or abilities gained
        
        For each important piece of information, provide:
        - Type: character/world/event/relationship
        - Title: Brief descriptive title
        - Content: Detailed description
        - Importance: Score from 1-10
        - Characters involved
        - Locations involved
        
        Only extract information that would be important to remember for future story continuity.
        """
        
        try:
            response = self.llm.invoke([SystemMessage(content=extraction_prompt)])
            
            # In a production system, you would parse the LLM response
            # and extract structured memory data to store
            # For now, we'll store a general memory
            
            if len(conversation_text) > 100:  # Only store if substantial content
                self.store_memory(
                    session_id=state["session_id"],
                    memory_type="event",
                    title="Recent Story Development",
                    content=conversation_text[:500],  # Truncate for storage
                    importance=5.0,
                    related_characters=[state["player_character"].name] if state["player_character"] else [],
                    related_locations=[state["world_context"].current_location],
                    tags=["recent", "story_development"]
                )
                
        except Exception as e:
            logger.error(f"Error extracting memories: {e}")
    
    def _messages_to_text(self, messages: List[BaseMessage]) -> str:
        """
        Convert messages to text format.
        
        Args:
            messages: List of messages
            
        Returns:
            str: Formatted conversation text
        """
        text_parts = []
        for msg in messages:
            if isinstance(msg, HumanMessage):
                text_parts.append(f"Player: {msg.content}")
            elif isinstance(msg, AIMessage):
                text_parts.append(f"Game Master: {msg.content}")
            elif isinstance(msg, SystemMessage):
                text_parts.append(f"System: {msg.content}")
        
        return "\n".join(text_parts)
    
    def get_memory_stats(self, session_id: str) -> Dict[str, Any]:
        """
        Get memory statistics for a session.
        
        Args:
            session_id: Game session ID
            
        Returns:
            Dict with memory statistics
        """
        try:
            with self.db_manager.get_session() as session:
                memories = session.query(Memory).filter(
                    Memory.session_id == session_id
                ).all()
                
                stats = {
                    "total_memories": len(memories),
                    "memory_types": {},
                    "average_importance": 0.0,
                    "most_accessed": None
                }
                
                if memories:
                    # Count by type
                    for memory in memories:
                        mem_type = memory.memory_type
                        stats["memory_types"][mem_type] = stats["memory_types"].get(mem_type, 0) + 1
                    
                    # Average importance
                    stats["average_importance"] = sum(m.importance_score for m in memories) / len(memories)
                    
                    # Most accessed
                    most_accessed = max(memories, key=lambda m: m.access_count)
                    stats["most_accessed"] = {
                        "title": most_accessed.title,
                        "access_count": most_accessed.access_count
                    }
                
                return stats
                
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {"error": str(e)}
