"""
Character management system for the RPG engine.

This module handles character creation, persistence, personality modeling,
and relationship tracking for both player characters and NPCs.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import random
from datetime import datetime

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import SystemMessage

from ..database import get_database_manager, Character
from ..core.state import PlayerCharacter

logger = logging.getLogger(__name__)


class RelationshipType(Enum):
    """Types of relationships between characters."""
    FRIENDLY = "friendly"
    HOSTILE = "hostile"
    NEUTRAL = "neutral"
    ROMANTIC = "romantic"
    FAMILY = "family"
    MENTOR = "mentor"
    RIVAL = "rival"
    ALLY = "ally"
    ENEMY = "enemy"


@dataclass
class NPCPersonality:
    """NPC personality model."""
    traits: List[str]
    motivations: List[str]
    fears: List[str]
    speech_patterns: List[str]
    behavioral_quirks: List[str]
    moral_alignment: str  # lawful/neutral/chaotic + good/neutral/evil


@dataclass
class Relationship:
    """Relationship between two characters."""
    character_a: str
    character_b: str
    relationship_type: RelationshipType
    strength: int  # 1-10 scale
    history: List[str]  # Key events in the relationship
    last_interaction: Optional[datetime] = None


class CharacterManager:
    """
    Manages character creation, development, and interactions.

    This class handles:
    - Player character creation and customization
    - NPC generation and personality modeling
    - Character relationship tracking
    - Character development and progression
    - Personality-driven dialogue generation
    """

    def __init__(self, llm: BaseChatModel):
        """
        Initialize the character manager.

        Args:
            llm: Language model for character generation and dialogue
        """
        self.llm = llm
        self.db_manager = get_database_manager()

        # Character generation templates
        self.personality_traits = [
            "brave", "cautious", "curious", "stubborn", "kind", "cynical",
            "optimistic", "pessimistic", "loyal", "independent", "creative",
            "analytical", "impulsive", "patient", "ambitious", "humble"
        ]

        self.backgrounds = [
            "noble", "commoner", "merchant", "scholar", "soldier", "criminal",
            "hermit", "entertainer", "artisan", "farmer", "sailor", "hunter"
        ]

        self.skills = [
            "combat", "magic", "stealth", "diplomacy", "crafting", "survival",
            "investigation", "athletics", "performance", "medicine", "history"
        ]

        logger.info("Character manager initialized")

    def create_player_character(
        self,
        session_id: str,
        name: str,
        description: str = None,
        background: str = None,
        personality_traits: List[str] = None,
        custom_skills: Dict[str, int] = None
    ) -> PlayerCharacter:
        """
        Create a new player character.

        Args:
            session_id: Game session ID
            name: Character name
            description: Character description
            background: Character background
            personality_traits: List of personality traits
            custom_skills: Custom skill levels

        Returns:
            PlayerCharacter: Created character
        """
        # Generate missing attributes
        if not description:
            description = self._generate_character_description(name, background)

        if not background:
            background = random.choice(self.backgrounds)

        if not personality_traits:
            personality_traits = random.sample(self.personality_traits, 3)

        # Generate skills
        skills = custom_skills or self._generate_character_skills(background)

        # Create character
        character = PlayerCharacter(
            name=name,
            description=description,
            background=background,
            personality_traits=personality_traits,
            skills=skills,
            inventory=self._generate_starting_inventory(background),
            current_location="starting_village",
            health=100,
            experience=0,
            relationships={}
        )

        # Save to database
        self._save_character_to_db(session_id, character, "player")

        logger.info(f"Created player character: {name}")
        return character

    def generate_npc(
        self,
        session_id: str,
        location: str,
        role: str = None,
        personality_type: str = None
    ) -> Dict[str, Any]:
        """
        Generate a new NPC for the world.

        Args:
            session_id: Game session ID
            location: NPC's location
            role: NPC's role (guard, merchant, etc.)
            personality_type: Specific personality type

        Returns:
            Dict containing NPC information
        """
        # Generate basic attributes
        name = self._generate_npc_name()
        role = role or self._generate_npc_role()

        # Generate personality
        personality = self._generate_npc_personality(personality_type)

        # Generate description based on role and personality
        description = self._generate_npc_description(name, role, personality)

        # Create NPC data
        npc_data = {
            "name": name,
            "description": description,
            "background": role,
            "personality_traits": personality.traits,
            "skills": self._generate_npc_skills(role),
            "inventory": self._generate_npc_inventory(role),
            "current_location": location,
            "health": random.randint(80, 120),
            "experience": random.randint(0, 100),
            "relationships": {},
            "personality": {
                "motivations": personality.motivations,
                "fears": personality.fears,
                "speech_patterns": personality.speech_patterns,
                "behavioral_quirks": personality.behavioral_quirks,
                "moral_alignment": personality.moral_alignment
            }
        }

        # Save to database
        self._save_npc_to_db(session_id, npc_data)

        logger.info(f"Generated NPC: {name} ({role}) at {location}")
        return npc_data

    def update_relationship(
        self,
        session_id: str,
        character_a: str,
        character_b: str,
        relationship_type: RelationshipType,
        strength_change: int = 0,
        interaction_description: str = None
    ):
        """
        Update relationship between two characters.

        Args:
            session_id: Game session ID
            character_a: First character name
            character_b: Second character name
            relationship_type: Type of relationship
            strength_change: Change in relationship strength
            interaction_description: Description of the interaction
        """
        try:
            with self.db_manager.get_session() as session:
                # Get both characters
                char_a = session.query(Character).filter_by(
                    session_id=session_id, name=character_a
                ).first()

                char_b = session.query(Character).filter_by(
                    session_id=session_id, name=character_b
                ).first()

                if not char_a or not char_b:
                    logger.warning(f"Could not find characters for relationship update: {character_a}, {character_b}")
                    return

                # Update relationships
                self._update_character_relationship(char_a, character_b, relationship_type, strength_change)
                self._update_character_relationship(char_b, character_a, relationship_type, strength_change)

                if interaction_description:
                    # Add to relationship history
                    self._add_relationship_history(char_a, character_b, interaction_description)
                    self._add_relationship_history(char_b, character_a, interaction_description)

                logger.info(f"Updated relationship: {character_a} <-> {character_b}")

        except Exception as e:
            logger.error(f"Error updating relationship: {e}")

    def generate_npc_dialogue(
        self,
        session_id: str,
        npc_name: str,
        context: str,
        player_input: str = None
    ) -> str:
        """
        Generate dialogue for an NPC based on their personality.

        Args:
            session_id: Game session ID
            npc_name: NPC name
            context: Current context/situation
            player_input: What the player said (if any)

        Returns:
            str: Generated NPC dialogue
        """
        try:
            # Get NPC data
            npc_data = self._get_character_from_db(session_id, npc_name)
            if not npc_data:
                return f"{npc_name} doesn't respond."

            # Build personality context
            personality_context = self._build_personality_context(npc_data)

            dialogue_prompt = f"""
            Generate dialogue for {npc_name}, an NPC with the following characteristics:

            {personality_context}

            Current situation: {context}
            {f"Player said: {player_input}" if player_input else ""}

            Generate a response that:
            1. Reflects the NPC's personality and speech patterns
            2. Is appropriate for the situation
            3. Advances the conversation or story
            4. Stays in character

            Response should be 1-3 sentences.
            """

            response = self.llm.invoke([SystemMessage(content=dialogue_prompt)])
            return response.content

        except Exception as e:
            logger.error(f"Error generating NPC dialogue: {e}")
            return f"{npc_name} seems distracted and doesn't respond clearly."

    def get_character_relationships(self, session_id: str, character_name: str) -> Dict[str, Any]:
        """
        Get all relationships for a character.

        Args:
            session_id: Game session ID
            character_name: Character name

        Returns:
            Dict containing relationship information
        """
        try:
            character_data = self._get_character_from_db(session_id, character_name)
            if not character_data:
                return {}

            return character_data.get("relationships", {})

        except Exception as e:
            logger.error(f"Error getting character relationships: {e}")
            return {}

    def _generate_character_description(self, name: str, background: str = None) -> str:
        """Generate a character description."""
        prompt = f"""
        Generate a brief physical description for a character named {name}.
        {f"They have a {background} background." if background else ""}

        Include:
        - Physical appearance (height, build, hair, eyes, etc.)
        - Notable features or distinguishing marks
        - General demeanor or presence

        Keep it to 2-3 sentences.
        """

        try:
            response = self.llm.invoke([SystemMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Error generating character description: {e}")
            return f"A {background or 'mysterious'} individual with an unremarkable appearance."

    def _generate_character_skills(self, background: str) -> Dict[str, int]:
        """Generate character skills based on background."""
        skills = {skill: random.randint(1, 3) for skill in self.skills}

        # Boost skills based on background
        background_bonuses = {
            "soldier": {"combat": 3, "athletics": 2},
            "scholar": {"magic": 3, "history": 2},
            "criminal": {"stealth": 3, "investigation": 2},
            "merchant": {"diplomacy": 3, "crafting": 2},
            "noble": {"diplomacy": 2, "history": 2},
            "hermit": {"survival": 3, "medicine": 2}
        }

        if background in background_bonuses:
            for skill, bonus in background_bonuses[background].items():
                skills[skill] = min(10, skills[skill] + bonus)

        return skills

    def _generate_starting_inventory(self, background: str) -> List[str]:
        """Generate starting inventory based on background."""
        base_items = ["basic clothes", "small pouch"]

        background_items = {
            "soldier": ["sword", "shield", "armor"],
            "scholar": ["spellbook", "ink and quill", "scroll case"],
            "criminal": ["lockpicks", "dagger", "rope"],
            "merchant": ["coin purse", "trade goods", "ledger"],
            "noble": ["fine clothes", "signet ring", "purse of gold"],
            "hermit": ["staff", "herb pouch", "survival kit"]
        }

        items = base_items + background_items.get(background, ["simple tool"])
        return items

    def _generate_npc_name(self) -> str:
        """Generate a random NPC name."""
        first_names = [
            "Aiden", "Brenna", "Cael", "Dara", "Eamon", "Fiona", "Gareth", "Isla",
            "Kieran", "Liora", "Magnus", "Nora", "Owen", "Petra", "Quinn", "Rhea"
        ]

        last_names = [
            "Blackwood", "Stormwind", "Ironforge", "Goldleaf", "Shadowmere",
            "Brightblade", "Moonwhisper", "Thornfield", "Riverstone", "Flameheart"
        ]

        return f"{random.choice(first_names)} {random.choice(last_names)}"

    def _generate_npc_role(self) -> str:
        """Generate a random NPC role."""
        roles = [
            "guard", "merchant", "innkeeper", "blacksmith", "healer", "scholar",
            "farmer", "hunter", "bard", "priest", "noble", "beggar", "thief"
        ]
        return random.choice(roles)

    def _generate_npc_personality(self, personality_type: str = None) -> NPCPersonality:
        """Generate NPC personality."""
        if personality_type:
            # Use specific personality type
            traits = self._get_personality_traits_for_type(personality_type)
        else:
            traits = random.sample(self.personality_traits, random.randint(2, 4))

        motivations = random.sample([
            "wealth", "power", "knowledge", "love", "revenge", "justice",
            "freedom", "security", "fame", "peace", "adventure", "family"
        ], random.randint(1, 3))

        fears = random.sample([
            "death", "failure", "loneliness", "poverty", "darkness", "magic",
            "authority", "change", "betrayal", "loss", "pain", "unknown"
        ], random.randint(1, 2))

        speech_patterns = random.sample([
            "formal", "casual", "poetic", "blunt", "verbose", "terse",
            "cheerful", "gloomy", "sarcastic", "earnest"
        ], random.randint(1, 2))

        quirks = random.sample([
            "taps fingers when thinking", "always adjusts clothing", "speaks to animals",
            "collects unusual objects", "hums while working", "avoids eye contact",
            "gestures dramatically", "quotes old sayings", "counts things obsessively"
        ], random.randint(0, 2))

        alignments = [
            "lawful good", "neutral good", "chaotic good",
            "lawful neutral", "true neutral", "chaotic neutral",
            "lawful evil", "neutral evil", "chaotic evil"
        ]

        return NPCPersonality(
            traits=traits,
            motivations=motivations,
            fears=fears,
            speech_patterns=speech_patterns,
            behavioral_quirks=quirks,
            moral_alignment=random.choice(alignments)
        )

    def _get_personality_traits_for_type(self, personality_type: str) -> List[str]:
        """Get personality traits for a specific type."""
        type_traits = {
            "friendly": ["kind", "optimistic", "loyal"],
            "hostile": ["cynical", "stubborn", "independent"],
            "mysterious": ["cautious", "analytical", "independent"],
            "cheerful": ["optimistic", "kind", "creative"],
            "grumpy": ["pessimistic", "stubborn", "cynical"]
        }
        return type_traits.get(personality_type, random.sample(self.personality_traits, 3))

    def _generate_npc_description(self, name: str, role: str, personality: NPCPersonality) -> str:
        """Generate NPC description based on role and personality."""
        prompt = f"""
        Generate a brief description for an NPC named {name} who is a {role}.

        Personality traits: {', '.join(personality.traits)}
        Moral alignment: {personality.moral_alignment}

        Include:
        - Physical appearance appropriate for their role
        - How their personality shows in their appearance/demeanor
        - Any notable features

        Keep it to 2-3 sentences.
        """

        try:
            response = self.llm.invoke([SystemMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Error generating NPC description: {e}")
            return f"A {role} with a {personality.moral_alignment} demeanor."

    def _generate_npc_skills(self, role: str) -> Dict[str, int]:
        """Generate NPC skills based on role."""
        skills = {skill: random.randint(1, 5) for skill in self.skills}

        role_bonuses = {
            "guard": {"combat": 4, "athletics": 3},
            "merchant": {"diplomacy": 4, "crafting": 3},
            "scholar": {"magic": 4, "history": 4},
            "blacksmith": {"crafting": 5, "athletics": 3},
            "healer": {"medicine": 5, "magic": 3},
            "thief": {"stealth": 4, "investigation": 3}
        }

        if role in role_bonuses:
            for skill, bonus in role_bonuses[role].items():
                skills[skill] = min(10, skills[skill] + bonus)

        return skills

    def _generate_npc_inventory(self, role: str) -> List[str]:
        """Generate NPC inventory based on role."""
        base_items = ["clothes", "personal belongings"]

        role_items = {
            "guard": ["sword", "armor", "shield"],
            "merchant": ["trade goods", "coin purse", "ledger"],
            "blacksmith": ["hammer", "tongs", "metal ingots"],
            "healer": ["healing herbs", "bandages", "medicine kit"],
            "scholar": ["books", "scrolls", "writing materials"],
            "thief": ["lockpicks", "dagger", "rope"]
        }

        return base_items + role_items.get(role, ["tools"])

    def _save_character_to_db(self, session_id: str, character: PlayerCharacter, char_type: str):
        """Save character to database."""
        try:
            with self.db_manager.get_session() as session:
                char_record = Character(
                    session_id=session_id,
                    character_type=char_type,
                    name=character.name,
                    description=character.description,
                    background=character.background,
                    personality_traits=character.personality_traits,
                    skills=character.skills,
                    inventory=character.inventory,
                    current_location=character.current_location,
                    health=character.health,
                    experience=character.experience,
                    relationships=character.relationships,
                    world_id=1  # Default world ID
                )
                session.add(char_record)

        except Exception as e:
            logger.error(f"Error saving character to database: {e}")

    def _save_npc_to_db(self, session_id: str, npc_data: Dict[str, Any]):
        """Save NPC to database."""
        try:
            with self.db_manager.get_session() as session:
                npc_record = Character(
                    session_id=session_id,
                    character_type="npc",
                    name=npc_data["name"],
                    description=npc_data["description"],
                    background=npc_data["background"],
                    personality_traits=npc_data["personality_traits"],
                    skills=npc_data["skills"],
                    inventory=npc_data["inventory"],
                    current_location=npc_data["current_location"],
                    health=npc_data["health"],
                    experience=npc_data["experience"],
                    relationships=npc_data["relationships"],
                    world_id=1  # Default world ID
                )
                session.add(npc_record)

        except Exception as e:
            logger.error(f"Error saving NPC to database: {e}")

    def _get_character_from_db(self, session_id: str, character_name: str) -> Optional[Dict[str, Any]]:
        """Get character data from database."""
        try:
            with self.db_manager.get_session() as session:
                character = session.query(Character).filter_by(
                    session_id=session_id,
                    name=character_name
                ).first()

                if not character:
                    return None

                return {
                    "name": character.name,
                    "description": character.description,
                    "background": character.background,
                    "personality_traits": character.personality_traits,
                    "skills": character.skills,
                    "inventory": character.inventory,
                    "current_location": character.current_location,
                    "health": character.health,
                    "experience": character.experience,
                    "relationships": character.relationships
                }

        except Exception as e:
            logger.error(f"Error getting character from database: {e}")
            return None

    def _update_character_relationship(
        self,
        character: Character,
        other_name: str,
        relationship_type: RelationshipType,
        strength_change: int
    ):
        """Update a character's relationship with another character."""
        relationships = character.relationships or {}

        if other_name not in relationships:
            relationships[other_name] = {
                "type": relationship_type.value,
                "strength": 5,  # Neutral starting point
                "history": []
            }

        # Update relationship
        relationships[other_name]["type"] = relationship_type.value
        current_strength = relationships[other_name]["strength"]
        new_strength = max(1, min(10, current_strength + strength_change))
        relationships[other_name]["strength"] = new_strength

        character.relationships = relationships

    def _add_relationship_history(self, character: Character, other_name: str, event: str):
        """Add an event to relationship history."""
        relationships = character.relationships or {}

        if other_name in relationships:
            if "history" not in relationships[other_name]:
                relationships[other_name]["history"] = []

            relationships[other_name]["history"].append({
                "event": event,
                "timestamp": datetime.now().isoformat()
            })

            # Keep only last 10 events
            if len(relationships[other_name]["history"]) > 10:
                relationships[other_name]["history"] = relationships[other_name]["history"][-10:]

            character.relationships = relationships

    def _build_personality_context(self, character_data: Dict[str, Any]) -> str:
        """Build personality context for dialogue generation."""
        context_parts = [
            f"Name: {character_data['name']}",
            f"Background: {character_data['background']}",
            f"Personality traits: {', '.join(character_data['personality_traits'])}",
            f"Description: {character_data['description']}"
        ]

        return "\n".join(context_parts)
