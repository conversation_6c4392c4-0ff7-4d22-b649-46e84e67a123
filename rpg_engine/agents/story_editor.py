"""
Story editor agent for maintaining narrative consistency.

This module contains an AI agent that reviews and validates story content
to ensure consistency, appropriate tone, and quality storytelling.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import SystemMessage

from ..core.state import GameState

logger = logging.getLogger(__name__)


class ValidationResult(Enum):
    """Results of content validation."""
    APPROVED = "approved"
    NEEDS_REVISION = "needs_revision"
    REJECTED = "rejected"


class ContentIssue(Enum):
    """Types of content issues."""
    TONE_INCONSISTENCY = "tone_inconsistency"
    CHARACTER_INCONSISTENCY = "character_inconsistency"
    WORLD_INCONSISTENCY = "world_inconsistency"
    PLOT_HOLE = "plot_hole"
    PACING_ISSUE = "pacing_issue"
    INAPPROPRIATE_CONTENT = "inappropriate_content"
    QUALITY_ISSUE = "quality_issue"


@dataclass
class ValidationFeedback:
    """Feedback from content validation."""
    result: ValidationResult
    issues: List[ContentIssue]
    feedback_text: str
    suggestions: List[str]
    confidence: float  # 0-1 confidence in the assessment


class StoryEditorAgent:
    """
    AI agent that maintains narrative consistency and quality.
    
    This agent reviews generated content to ensure:
    - Consistent tone and style
    - Character consistency
    - World state consistency
    - Plot coherence
    - Appropriate pacing
    - Content quality
    """
    
    def __init__(self, llm: BaseChatModel):
        """
        Initialize the story editor agent.
        
        Args:
            llm: Language model for content analysis
        """
        self.llm = llm
        
        # Story guidelines and preferences
        self.tone_guidelines = {
            "adventurous": "Exciting, bold, with a sense of discovery and heroism",
            "mysterious": "Intriguing, atmospheric, with hidden secrets and suspense",
            "dark": "Serious, gritty, with mature themes and consequences",
            "lighthearted": "Fun, optimistic, with humor and positive outcomes",
            "epic": "Grand scale, heroic deeds, world-changing events",
            "intimate": "Personal, character-focused, emotional depth"
        }
        
        # Content quality criteria
        self.quality_criteria = [
            "Clear and engaging writing",
            "Logical progression of events",
            "Consistent character behavior",
            "Appropriate pacing",
            "Immersive descriptions",
            "Meaningful player choices"
        ]
        
        logger.info("Story editor agent initialized")
    
    def validate_content(self, state: GameState) -> GameState:
        """
        Validate generated content for consistency and quality.
        
        Args:
            state: Current game state with generated content
            
        Returns:
            GameState: Updated state with validation results
        """
        if not state.get("generated_response"):
            state["content_approved"] = True
            return state
        
        try:
            # Perform validation
            feedback = self._validate_response(state)
            
            # Update state based on validation
            if feedback.result == ValidationResult.APPROVED:
                state["content_approved"] = True
                state["editor_feedback"] = feedback.feedback_text
            else:
                state["content_approved"] = False
                state["editor_feedback"] = feedback.feedback_text
                
                # Increment retry count
                state["retry_count"] = state.get("retry_count", 0) + 1
                
                # If too many retries, approve with warning
                if state["retry_count"] >= 3:
                    state["content_approved"] = True
                    state["editor_feedback"] = "Content approved after multiple revisions."
                    logger.warning(f"Content approved after {state['retry_count']} retries")
            
            logger.info(f"Content validation: {feedback.result.value}")
            return state
            
        except Exception as e:
            logger.error(f"Error in content validation: {e}")
            # Default to approval on error
            state["content_approved"] = True
            state["editor_feedback"] = "Content approved (validation error occurred)."
            return state
    
    def _validate_response(self, state: GameState) -> ValidationFeedback:
        """
        Validate a generated response.
        
        Args:
            state: Current game state
            
        Returns:
            ValidationFeedback: Validation results and feedback
        """
        content = state["generated_response"]
        
        # Build context for validation
        context = self._build_validation_context(state)
        
        validation_prompt = f"""
        You are a story editor reviewing content for an RPG game. Analyze the following generated content for quality and consistency.
        
        GENERATED CONTENT:
        {content}
        
        CONTEXT:
        {context}
        
        EVALUATION CRITERIA:
        1. Tone Consistency: Does the content match the established narrative tone?
        2. Character Consistency: Are character actions and dialogue consistent with their established personality?
        3. World Consistency: Does the content respect established world rules and state?
        4. Plot Coherence: Does the content logically follow from previous events?
        5. Quality: Is the writing clear, engaging, and well-paced?
        6. Appropriateness: Is the content appropriate for the game's tone and audience?
        
        RESPONSE FORMAT:
        Result: [APPROVED/NEEDS_REVISION/REJECTED]
        Issues: [List any specific issues found]
        Feedback: [Detailed feedback explaining the assessment]
        Suggestions: [Specific suggestions for improvement if needed]
        Confidence: [0.0-1.0 confidence in this assessment]
        
        Be constructive and specific in your feedback.
        """
        
        try:
            response = self.llm.invoke([SystemMessage(content=validation_prompt)])
            return self._parse_validation_response(response.content)
            
        except Exception as e:
            logger.error(f"Error in LLM validation: {e}")
            return ValidationFeedback(
                result=ValidationResult.APPROVED,
                issues=[],
                feedback_text="Validation error occurred, content approved by default.",
                suggestions=[],
                confidence=0.0
            )
    
    def _build_validation_context(self, state: GameState) -> str:
        """
        Build context string for validation.
        
        Args:
            state: Current game state
            
        Returns:
            str: Context information for validation
        """
        context_parts = []
        
        # Game phase and action type
        context_parts.append(f"Game Phase: {state.get('game_phase', 'unknown')}")
        context_parts.append(f"Action Type: {state.get('action_type', 'unknown')}")
        
        # Story context
        story_context = state.get("story_context")
        if story_context:
            context_parts.append(f"Narrative Tone: {story_context.narrative_tone}")
            context_parts.append(f"Current Scene: {story_context.current_scene}")
            context_parts.append(f"Story Themes: {', '.join(story_context.story_themes)}")
            
            if story_context.recent_events:
                context_parts.append(f"Recent Events: {', '.join(story_context.recent_events[-3:])}")
        
        # Character context
        player_character = state.get("player_character")
        if player_character:
            context_parts.append(f"Player Character: {player_character.name}")
            context_parts.append(f"Character Traits: {', '.join(player_character.personality_traits)}")
            context_parts.append(f"Current Location: {player_character.current_location}")
        
        # World context
        world_context = state.get("world_context")
        if world_context:
            context_parts.append(f"World Location: {world_context.current_location}")
            context_parts.append(f"Atmosphere: {world_context.atmosphere}")
            context_parts.append(f"Time: {world_context.time_of_day}")
            context_parts.append(f"Weather: {world_context.weather}")
        
        # Recent conversation
        messages = state.get("messages", [])
        if len(messages) > 1:
            recent_messages = messages[-3:-1]  # Exclude the current response
            conversation_text = "\n".join([
                f"{'Player' if hasattr(msg, 'content') and msg.__class__.__name__ == 'HumanMessage' else 'Game'}: {msg.content}"
                for msg in recent_messages if hasattr(msg, 'content')
            ])
            context_parts.append(f"Recent Conversation:\n{conversation_text}")
        
        return "\n".join(context_parts)
    
    def _parse_validation_response(self, response_text: str) -> ValidationFeedback:
        """
        Parse the LLM's validation response.
        
        Args:
            response_text: Raw response from LLM
            
        Returns:
            ValidationFeedback: Parsed feedback
        """
        try:
            lines = response_text.strip().split('\n')
            
            # Default values
            result = ValidationResult.APPROVED
            issues = []
            feedback_text = "Content appears acceptable."
            suggestions = []
            confidence = 0.8
            
            # Parse response
            for line in lines:
                line = line.strip()
                
                if line.startswith("Result:"):
                    result_text = line.split(":", 1)[1].strip().upper()
                    if "NEEDS_REVISION" in result_text:
                        result = ValidationResult.NEEDS_REVISION
                    elif "REJECTED" in result_text:
                        result = ValidationResult.REJECTED
                    else:
                        result = ValidationResult.APPROVED
                
                elif line.startswith("Issues:"):
                    issues_text = line.split(":", 1)[1].strip()
                    if issues_text and issues_text.lower() != "none":
                        # Parse issues (simplified)
                        if "tone" in issues_text.lower():
                            issues.append(ContentIssue.TONE_INCONSISTENCY)
                        if "character" in issues_text.lower():
                            issues.append(ContentIssue.CHARACTER_INCONSISTENCY)
                        if "world" in issues_text.lower():
                            issues.append(ContentIssue.WORLD_INCONSISTENCY)
                        if "plot" in issues_text.lower():
                            issues.append(ContentIssue.PLOT_HOLE)
                        if "quality" in issues_text.lower():
                            issues.append(ContentIssue.QUALITY_ISSUE)
                
                elif line.startswith("Feedback:"):
                    feedback_text = line.split(":", 1)[1].strip()
                
                elif line.startswith("Suggestions:"):
                    suggestions_text = line.split(":", 1)[1].strip()
                    if suggestions_text and suggestions_text.lower() != "none":
                        suggestions = [s.strip() for s in suggestions_text.split(",")]
                
                elif line.startswith("Confidence:"):
                    try:
                        confidence = float(line.split(":", 1)[1].strip())
                        confidence = max(0.0, min(1.0, confidence))  # Clamp to 0-1
                    except ValueError:
                        confidence = 0.8
            
            return ValidationFeedback(
                result=result,
                issues=issues,
                feedback_text=feedback_text,
                suggestions=suggestions,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Error parsing validation response: {e}")
            return ValidationFeedback(
                result=ValidationResult.APPROVED,
                issues=[],
                feedback_text="Could not parse validation response, content approved by default.",
                suggestions=[],
                confidence=0.0
            )
    
    def get_tone_guidelines(self, tone: str) -> str:
        """
        Get guidelines for a specific narrative tone.
        
        Args:
            tone: Narrative tone name
            
        Returns:
            str: Guidelines for the tone
        """
        return self.tone_guidelines.get(tone, "Maintain consistent narrative voice and style.")
    
    def suggest_improvements(self, content: str, issues: List[ContentIssue]) -> List[str]:
        """
        Generate specific improvement suggestions.
        
        Args:
            content: Content to improve
            issues: List of identified issues
            
        Returns:
            List of improvement suggestions
        """
        suggestions = []
        
        for issue in issues:
            if issue == ContentIssue.TONE_INCONSISTENCY:
                suggestions.append("Adjust the tone to match the established narrative style")
            elif issue == ContentIssue.CHARACTER_INCONSISTENCY:
                suggestions.append("Ensure character actions align with their established personality")
            elif issue == ContentIssue.WORLD_INCONSISTENCY:
                suggestions.append("Check that content respects established world rules and state")
            elif issue == ContentIssue.PLOT_HOLE:
                suggestions.append("Address logical inconsistencies in the plot progression")
            elif issue == ContentIssue.PACING_ISSUE:
                suggestions.append("Adjust pacing to maintain engagement without rushing")
            elif issue == ContentIssue.QUALITY_ISSUE:
                suggestions.append("Improve clarity, engagement, and descriptive quality")
        
        return suggestions
    
    def analyze_story_progression(self, state: GameState) -> Dict[str, Any]:
        """
        Analyze overall story progression and provide insights.
        
        Args:
            state: Current game state
            
        Returns:
            Dict containing story analysis
        """
        try:
            story_context = state.get("story_context")
            if not story_context:
                return {"error": "No story context available"}
            
            analysis = {
                "current_scene": story_context.current_scene,
                "narrative_tone": story_context.narrative_tone,
                "active_themes": story_context.story_themes,
                "plot_threads": len(story_context.plot_threads),
                "story_length": len(state.get("messages", [])),
                "pacing_assessment": self._assess_pacing(state),
                "character_development": self._assess_character_development(state),
                "world_consistency": self._assess_world_consistency(state)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing story progression: {e}")
            return {"error": str(e)}
    
    def _assess_pacing(self, state: GameState) -> str:
        """Assess story pacing."""
        message_count = len(state.get("messages", []))
        
        if message_count < 5:
            return "early_stage"
        elif message_count < 20:
            return "building"
        elif message_count < 50:
            return "developing"
        else:
            return "advanced"
    
    def _assess_character_development(self, state: GameState) -> str:
        """Assess character development."""
        player_character = state.get("player_character")
        if not player_character:
            return "no_character"
        
        # Simple assessment based on experience and relationships
        if player_character.experience > 50 or len(player_character.relationships) > 3:
            return "well_developed"
        elif player_character.experience > 10 or len(player_character.relationships) > 1:
            return "developing"
        else:
            return "early_stage"
    
    def _assess_world_consistency(self, state: GameState) -> str:
        """Assess world consistency."""
        world_context = state.get("world_context")
        if not world_context:
            return "no_world"
        
        # Simple assessment based on world state complexity
        world_state = world_context.world_state
        if len(world_state.get("locations", {})) > 5:
            return "complex"
        elif len(world_state.get("locations", {})) > 2:
            return "developing"
        else:
            return "simple"
