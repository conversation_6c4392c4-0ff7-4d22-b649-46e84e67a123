"""
FastAPI application for the RPG engine.

This module provides REST API endpoints for interacting with the
collaborative story creation engine.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import os
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import uvicorn

from langchain_openai import ChatOpenAI
from langchain_anthropic import Chat<PERSON>nthropic

from ..core.game_engine import RPGEngine
from ..database import init_database
from ..memory.manager import MemoryManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Pydantic models for API
class GameStartRequest(BaseModel):
    """Request to start a new game."""
    user_id: str = Field(..., description="Unique user identifier")
    session_id: Optional[str] = Field(None, description="Optional session ID")


class GameStartResponse(BaseModel):
    """Response for starting a new game."""
    session_id: str = Field(..., description="Game session ID")
    message: str = Field(..., description="Welcome message")


class UserInputRequest(BaseModel):
    """Request for user input."""
    session_id: str = Field(..., description="Game session ID")
    user_input: str = Field(..., description="User's input message")


class GameResponse(BaseModel):
    """Response from the game."""
    response: str = Field(..., description="Game's response")
    session_id: str = Field(..., description="Game session ID")
    timestamp: datetime = Field(default_factory=datetime.now)


class GameStateResponse(BaseModel):
    """Current game state response."""
    session_id: str
    game_phase: str
    player_character: Optional[Dict[str, Any]]
    world_context: Dict[str, Any]
    story_context: Dict[str, Any]


class CharacterCreationRequest(BaseModel):
    """Request to create a character."""
    session_id: str
    name: str
    description: Optional[str] = None
    background: Optional[str] = None
    personality_traits: Optional[List[str]] = None
    skills: Optional[Dict[str, int]] = None


class MemoryStatsResponse(BaseModel):
    """Memory statistics response."""
    total_memories: int
    memory_types: Dict[str, int]
    average_importance: float
    most_accessed: Optional[Dict[str, Any]]


# Global RPG engine instance
rpg_engine: Optional[RPGEngine] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global rpg_engine
    
    # Startup
    logger.info("Starting RPG Engine API...")
    
    # Initialize database
    database_url = os.getenv("DATABASE_URL", "sqlite:///rpg_game.db")
    init_database(database_url)
    
    # Initialize LLM
    llm_provider = os.getenv("LLM_PROVIDER", "openai")
    if llm_provider == "openai":
        llm = ChatOpenAI(
            model=os.getenv("OPENAI_MODEL", "gpt-4"),
            api_key=os.getenv("OPENAI_API_KEY")
        )
    elif llm_provider == "anthropic":
        llm = ChatAnthropic(
            model=os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229"),
            api_key=os.getenv("ANTHROPIC_API_KEY")
        )
    else:
        raise ValueError(f"Unsupported LLM provider: {llm_provider}")
    
    # Initialize RPG engine
    rpg_engine = RPGEngine(
        llm=llm,
        database_url=database_url,
        checkpointer_path=os.getenv("CHECKPOINTER_PATH", "rpg_checkpoints.db")
    )
    
    logger.info("RPG Engine API started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down RPG Engine API...")


# Create FastAPI app
app = FastAPI(
    title="RPG Engine API",
    description="Collaborative story creation engine built with LangGraph",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def get_rpg_engine() -> RPGEngine:
    """Dependency to get the RPG engine instance."""
    if rpg_engine is None:
        raise HTTPException(status_code=500, detail="RPG engine not initialized")
    return rpg_engine


@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with basic HTML interface."""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>RPG Engine</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .chat-box { border: 1px solid #ccc; height: 400px; overflow-y: scroll; padding: 10px; margin: 10px 0; }
            .input-box { width: 100%; padding: 10px; margin: 10px 0; }
            .button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
            .button:hover { background: #005a87; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>RPG Engine - Collaborative Story Creation</h1>
            <p>Welcome to the RPG Engine! This is a collaborative story creation system built with LangGraph.</p>
            
            <h2>Features:</h2>
            <ul>
                <li>Dynamic character creation and development</li>
                <li>AI-generated world building</li>
                <li>Intelligent story editing and consistency checking</li>
                <li>Long-term memory and conversation summarization</li>
                <li>Persistent game state across sessions</li>
            </ul>
            
            <h2>API Endpoints:</h2>
            <ul>
                <li><strong>POST /game/start</strong> - Start a new game session</li>
                <li><strong>POST /game/input</strong> - Send user input to the game</li>
                <li><strong>GET /game/state/{session_id}</strong> - Get current game state</li>
                <li><strong>POST /character/create</strong> - Create a new character</li>
                <li><strong>GET /memory/stats/{session_id}</strong> - Get memory statistics</li>
            </ul>
            
            <p><a href="/docs">View API Documentation</a></p>
        </div>
    </body>
    </html>
    """
    return html_content


@app.post("/game/start", response_model=GameStartResponse)
async def start_game(
    request: GameStartRequest,
    engine: RPGEngine = Depends(get_rpg_engine)
):
    """Start a new RPG game session."""
    try:
        session_id = engine.start_new_game(
            user_id=request.user_id,
            session_id=request.session_id
        )
        
        # Get initial welcome message
        welcome_message = engine.process_user_input(session_id, "")
        
        return GameStartResponse(
            session_id=session_id,
            message=welcome_message
        )
        
    except Exception as e:
        logger.error(f"Error starting game: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/game/input", response_model=GameResponse)
async def process_input(
    request: UserInputRequest,
    engine: RPGEngine = Depends(get_rpg_engine)
):
    """Process user input and generate response."""
    try:
        response = engine.process_user_input(
            session_id=request.session_id,
            user_input=request.user_input
        )
        
        return GameResponse(
            response=response,
            session_id=request.session_id
        )
        
    except Exception as e:
        logger.error(f"Error processing input: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/game/state/{session_id}", response_model=GameStateResponse)
async def get_game_state(
    session_id: str,
    engine: RPGEngine = Depends(get_rpg_engine)
):
    """Get current game state."""
    try:
        state = engine.get_game_state(session_id)
        
        if not state:
            raise HTTPException(status_code=404, detail="Game session not found")
        
        return GameStateResponse(
            session_id=session_id,
            game_phase=state.get("game_phase", "unknown"),
            player_character=state.get("player_character"),
            world_context=state.get("world_context", {}),
            story_context=state.get("story_context", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting game state: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/game/sessions/{user_id}")
async def list_sessions(
    user_id: str,
    engine: RPGEngine = Depends(get_rpg_engine)
):
    """List active game sessions for a user."""
    try:
        sessions = engine.list_active_sessions(user_id)
        return {"user_id": user_id, "sessions": sessions}
        
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/character/create")
async def create_character(
    request: CharacterCreationRequest,
    engine: RPGEngine = Depends(get_rpg_engine)
):
    """Create a new character."""
    try:
        # This would integrate with the character manager
        # For now, return a placeholder response
        return {
            "message": "Character creation endpoint - to be implemented",
            "session_id": request.session_id,
            "character_name": request.name
        }
        
    except Exception as e:
        logger.error(f"Error creating character: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/memory/stats/{session_id}", response_model=MemoryStatsResponse)
async def get_memory_stats(
    session_id: str,
    engine: RPGEngine = Depends(get_rpg_engine)
):
    """Get memory statistics for a session."""
    try:
        stats = engine.memory_manager.get_memory_stats(session_id)
        
        if "error" in stats:
            raise HTTPException(status_code=500, detail=stats["error"])
        
        return MemoryStatsResponse(**stats)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting memory stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "engine_initialized": rpg_engine is not None
    }


if __name__ == "__main__":
    # Run the server
    uvicorn.run(
        "rpg_engine.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
