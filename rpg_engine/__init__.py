"""
RPG Engine - A collaborative story creation engine built with LangGraph.

This package provides a complete role-playing game framework where AI creates
characters and builds worlds while users create and play their characters
in an interactive storytelling environment.
"""

__version__ = "1.0.0"
__author__ = "RPG Engine Team"

from .core.game_engine import RPGEngine
from .core.state import GameState
from .database.models import Character, World, Event, Conversation
from .agents.story_editor import StoryEditorAgent
from .memory.manager import MemoryManager

__all__ = [
    "RPGEngine",
    "GameState", 
    "Character",
    "World",
    "Event",
    "Conversation",
    "StoryEditorAgent",
    "MemoryManager"
]
