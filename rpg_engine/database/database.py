"""
Database connection and session management for the RPG engine.
"""

import os
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator
import logging

from .models import Base

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Manages database connections and sessions for the RPG engine.
    """
    
    def __init__(self, database_url: str = None):
        """
        Initialize the database manager.
        
        Args:
            database_url: SQLite database URL. If None, uses default location.
        """
        if database_url is None:
            # Default to local SQLite database
            db_path = os.path.join(os.getcwd(), "rpg_game.db")
            database_url = f"sqlite:///{db_path}"
        
        self.database_url = database_url
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize the database engine and session factory."""
        # Configure SQLite engine with proper settings
        if self.database_url.startswith("sqlite"):
            self.engine = create_engine(
                self.database_url,
                poolclass=StaticPool,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 20
                },
                echo=False  # Set to True for SQL debugging
            )
            
            # Enable foreign key constraints for SQLite
            @event.listens_for(self.engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA cache_size=10000")
                cursor.execute("PRAGMA temp_store=MEMORY")
                cursor.close()
        else:
            # For other databases (PostgreSQL, MySQL, etc.)
            self.engine = create_engine(self.database_url, echo=False)
        
        # Create session factory
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # Create all tables
        self.create_tables()
    
    def create_tables(self):
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all database tables. Use with caution!"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("Database tables dropped successfully")
        except Exception as e:
            logger.error(f"Error dropping database tables: {e}")
            raise
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """
        Get a database session with automatic cleanup.
        
        Yields:
            Session: SQLAlchemy database session
        """
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_session_sync(self) -> Session:
        """
        Get a database session for synchronous use.
        Remember to close the session when done.
        
        Returns:
            Session: SQLAlchemy database session
        """
        return self.SessionLocal()
    
    def close(self):
        """Close the database engine."""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connection closed")


# Global database manager instance
_db_manager = None


def get_database_manager(database_url: str = None) -> DatabaseManager:
    """
    Get the global database manager instance.
    
    Args:
        database_url: Database URL (only used on first call)
        
    Returns:
        DatabaseManager: The database manager instance
    """
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager(database_url)
    return _db_manager


def get_db_session() -> Generator[Session, None, None]:
    """
    Dependency function for getting database sessions.
    Useful for FastAPI dependency injection.
    
    Yields:
        Session: SQLAlchemy database session
    """
    db_manager = get_database_manager()
    with db_manager.get_session() as session:
        yield session


def init_database(database_url: str = None, reset: bool = False):
    """
    Initialize the database with optional reset.
    
    Args:
        database_url: Database URL
        reset: If True, drop and recreate all tables
    """
    db_manager = get_database_manager(database_url)
    
    if reset:
        logger.warning("Resetting database - all data will be lost!")
        db_manager.drop_tables()
        db_manager.create_tables()
    
    logger.info("Database initialized successfully")


if __name__ == "__main__":
    # Example usage and testing
    logging.basicConfig(level=logging.INFO)
    
    # Initialize database
    init_database(reset=True)
    
    # Test database connection
    db_manager = get_database_manager()
    with db_manager.get_session() as session:
        # Test query
        result = session.execute("SELECT 1").scalar()
        print(f"Database test successful: {result}")
    
    print("Database setup complete!")
