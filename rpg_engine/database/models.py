"""
Database models for the RPG engine.

This module defines SQLAlchemy models for persistent storage of game state,
characters, world information, and conversation history.
"""

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, Float,
    ForeignKey, JSON, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, List, Optional, Any
import json

Base = declarative_base()


class World(Base):
    """
    World table storing locations, events, and world state.
    """
    __tablename__ = "worlds"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(255), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Location information
    current_location = Column(String(255))
    available_locations = Column(JSON)  # List of location names
    location_descriptions = Column(JSON)  # Dict mapping locations to descriptions
    
    # World state
    world_state = Column(JSON)  # General world state data
    time_of_day = Column(String(50), default="morning")
    weather = Column(String(100), default="clear")
    atmosphere = Column(String(255), default="peaceful")
    
    # Active events and background processes
    active_events = Column(JSON)  # List of current events
    background_events = Column(JSON)  # List of background events
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    characters = relationship("Character", back_populates="world")
    events = relationship("Event", back_populates="world")
    conversations = relationship("Conversation", back_populates="world")
    
    __table_args__ = (
        Index('idx_world_session', 'session_id'),
        Index('idx_world_location', 'current_location'),
    )


class Character(Base):
    """
    Character table storing both player and NPC information.
    """
    __tablename__ = "characters"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    world_id = Column(Integer, ForeignKey("worlds.id"), nullable=False)
    session_id = Column(String(255), nullable=False, index=True)
    
    # Basic character info
    name = Column(String(255), nullable=False)
    character_type = Column(String(50), nullable=False)  # 'player' or 'npc'
    description = Column(Text)
    background = Column(Text)
    
    # Character attributes
    personality_traits = Column(JSON)  # List of personality traits
    skills = Column(JSON)  # Dict of skill name -> level
    inventory = Column(JSON)  # List of items
    
    # Status
    current_location = Column(String(255))
    health = Column(Integer, default=100)
    experience = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    
    # Relationships with other characters
    relationships = Column(JSON)  # Dict of character_name -> relationship_type
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    world = relationship("World", back_populates="characters")
    
    __table_args__ = (
        Index('idx_character_session', 'session_id'),
        Index('idx_character_type', 'character_type'),
        Index('idx_character_location', 'current_location'),
        Index('idx_character_name_session', 'name', 'session_id'),
    )


class Event(Base):
    """
    Events table storing world events, both active and historical.
    """
    __tablename__ = "events"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    world_id = Column(Integer, ForeignKey("worlds.id"), nullable=False)
    session_id = Column(String(255), nullable=False, index=True)
    
    # Event details
    name = Column(String(255), nullable=False)
    description = Column(Text)
    event_type = Column(String(100))  # 'background', 'active', 'completed'
    location = Column(String(255))
    
    # Event state
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=1)  # 1-10 priority scale
    
    # Participants and effects
    involved_characters = Column(JSON)  # List of character names
    world_effects = Column(JSON)  # Dict of world state changes
    
    # Timing
    start_time = Column(DateTime, default=func.now())
    end_time = Column(DateTime)
    duration_hours = Column(Integer)  # Expected duration in game hours
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    world = relationship("World", back_populates="events")
    
    __table_args__ = (
        Index('idx_event_session', 'session_id'),
        Index('idx_event_type', 'event_type'),
        Index('idx_event_location', 'location'),
        Index('idx_event_active', 'is_active'),
    )


class Conversation(Base):
    """
    Conversation history and memory storage.
    """
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    world_id = Column(Integer, ForeignKey("worlds.id"), nullable=False)
    session_id = Column(String(255), nullable=False, index=True)
    thread_id = Column(String(255), nullable=False, index=True)
    
    # Message content
    message_type = Column(String(50), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    
    # Context information
    action_type = Column(String(100))
    game_phase = Column(String(100))
    character_involved = Column(String(255))
    location = Column(String(255))
    
    # Memory and summarization
    is_summarized = Column(Boolean, default=False)
    summary_content = Column(Text)
    importance_score = Column(Float, default=1.0)  # 0-10 importance for memory
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    world = relationship("World", back_populates="conversations")
    
    __table_args__ = (
        Index('idx_conversation_session', 'session_id'),
        Index('idx_conversation_thread', 'thread_id'),
        Index('idx_conversation_type', 'message_type'),
        Index('idx_conversation_importance', 'importance_score'),
        Index('idx_conversation_created', 'created_at'),
    )


class Memory(Base):
    """
    Long-term memory storage for important information.
    """
    __tablename__ = "memories"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(255), nullable=False, index=True)
    
    # Memory content
    memory_type = Column(String(100), nullable=False)  # 'character', 'world', 'event', 'relationship'
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    
    # Context
    related_characters = Column(JSON)  # List of character names
    related_locations = Column(JSON)  # List of location names
    tags = Column(JSON)  # List of tags for categorization
    
    # Memory metadata
    importance_score = Column(Float, default=5.0)  # 0-10 importance scale
    access_count = Column(Integer, default=0)
    last_accessed = Column(DateTime)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index('idx_memory_session', 'session_id'),
        Index('idx_memory_type', 'memory_type'),
        Index('idx_memory_importance', 'importance_score'),
        Index('idx_memory_accessed', 'last_accessed'),
    )
