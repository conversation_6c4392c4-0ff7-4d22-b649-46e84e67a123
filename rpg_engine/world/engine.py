"""
World building engine for the RPG system.

This module handles dynamic world generation, location management,
background event systems, and world state tracking.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import random
from datetime import datetime, timedelta

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import SystemMessage

from ..database import get_database_manager, World, Event
from ..core.state import WorldContext

logger = logging.getLogger(__name__)


class LocationType(Enum):
    """Types of locations in the world."""
    VILLAGE = "village"
    CITY = "city"
    FOREST = "forest"
    MOUNTAIN = "mountain"
    CAVE = "cave"
    DUNGEON = "dungeon"
    CASTLE = "castle"
    TEMPLE = "temple"
    RUINS = "ruins"
    TAVERN = "tavern"
    SHOP = "shop"
    WILDERNESS = "wilderness"


class EventType(Enum):
    """Types of world events."""
    BACKGROUND = "background"
    ACTIVE = "active"
    QUEST = "quest"
    RANDOM = "random"
    SEASONAL = "seasonal"
    POLITICAL = "political"
    NATURAL = "natural"


@dataclass
class Location:
    """Represents a location in the world."""
    name: str
    location_type: LocationType
    description: str
    connections: List[str] = field(default_factory=list)
    npcs: List[str] = field(default_factory=list)
    items: List[str] = field(default_factory=list)
    events: List[str] = field(default_factory=list)
    atmosphere: str = "neutral"
    danger_level: int = 1  # 1-10 scale
    discovery_requirements: List[str] = field(default_factory=list)


@dataclass
class WorldEvent:
    """Represents an event in the world."""
    name: str
    event_type: EventType
    description: str
    location: str
    duration_hours: int
    effects: Dict[str, Any] = field(default_factory=dict)
    participants: List[str] = field(default_factory=list)
    prerequisites: List[str] = field(default_factory=list)
    consequences: List[str] = field(default_factory=list)
    is_active: bool = True
    priority: int = 5  # 1-10 scale


class WorldEngine:
    """
    Manages world generation, location creation, and event systems.

    This engine handles:
    - Dynamic location generation
    - World state management
    - Background event processing
    - Environmental storytelling
    - Location discovery and exploration
    """

    def __init__(self, llm: BaseChatModel):
        """
        Initialize the world engine.

        Args:
            llm: Language model for world generation
        """
        self.llm = llm
        self.db_manager = get_database_manager()

        # World generation templates
        self.location_templates = {
            LocationType.VILLAGE: {
                "names": ["Millbrook", "Oakenford", "Stonehaven", "Greendale", "Riverside"],
                "features": ["market square", "inn", "blacksmith", "temple", "well"],
                "atmospheres": ["peaceful", "bustling", "quiet", "friendly", "rustic"]
            },
            LocationType.FOREST: {
                "names": ["Whispering Woods", "Darkwood", "Silverleaf Forest", "Ancient Grove"],
                "features": ["ancient trees", "hidden paths", "wildlife", "streams", "clearings"],
                "atmospheres": ["mysterious", "peaceful", "ominous", "magical", "wild"]
            },
            LocationType.DUNGEON: {
                "names": ["Forgotten Crypt", "Shadow Depths", "Crystal Caverns", "Lost Temple"],
                "features": ["stone corridors", "treasure chambers", "traps", "monsters", "puzzles"],
                "atmospheres": ["dark", "dangerous", "mysterious", "ancient", "foreboding"]
            }
        }

        # Event templates
        self.event_templates = {
            EventType.BACKGROUND: [
                "merchant caravan arrives", "festival preparations", "weather changes",
                "seasonal migration", "trade negotiations", "construction project"
            ],
            EventType.RANDOM: [
                "strange lights in sky", "unusual animal behavior", "mysterious stranger",
                "lost traveler", "merchant in trouble", "discovery of artifact"
            ],
            EventType.QUEST: [
                "missing person", "monster threat", "ancient mystery", "political intrigue",
                "magical disturbance", "treasure hunt"
            ]
        }

        logger.info("World engine initialized")

    def generate_world(self, session_id: str, world_name: str = None) -> WorldContext:
        """
        Generate a new world for a game session.

        Args:
            session_id: Game session ID
            world_name: Optional world name

        Returns:
            WorldContext: Generated world context
        """
        if not world_name:
            world_name = self._generate_world_name()

        # Generate starting location
        starting_location = self._generate_location(LocationType.VILLAGE, "starting_village")

        # Generate connected locations
        connected_locations = self._generate_connected_locations(starting_location)

        # Create world context
        world_context = WorldContext(
            current_location=starting_location.name,
            available_locations=[loc.name for loc in connected_locations],
            active_npcs=[],
            current_events=[],
            world_state={
                "locations": {loc.name: self._location_to_dict(loc) for loc in connected_locations},
                "discovered_locations": [starting_location.name],
                "time_cycle": 0,
                "season": "spring"
            },
            time_of_day="morning",
            weather="clear",
            atmosphere=starting_location.atmosphere
        )

        # Save to database
        self._save_world_to_db(session_id, world_name, world_context)

        # Generate initial background events
        self._generate_background_events(session_id, connected_locations)

        logger.info(f"Generated world: {world_name}")
        return world_context

    def update_world_state(self, session_id: str, changes: Dict[str, Any]) -> bool:
        """
        Update world state with changes.

        Args:
            session_id: Game session ID
            changes: Dictionary of changes to apply

        Returns:
            bool: True if update successful
        """
        try:
            with self.db_manager.get_session() as session:
                world_record = session.query(World).filter_by(
                    session_id=session_id
                ).first()

                if not world_record:
                    logger.warning(f"World not found for session: {session_id}")
                    return False

                # Apply changes
                current_state = world_record.world_state or {}
                current_state.update(changes)
                world_record.world_state = current_state

                # Update other fields if provided
                if "current_location" in changes:
                    world_record.current_location = changes["current_location"]
                if "time_of_day" in changes:
                    world_record.time_of_day = changes["time_of_day"]
                if "weather" in changes:
                    world_record.weather = changes["weather"]
                if "atmosphere" in changes:
                    world_record.atmosphere = changes["atmosphere"]

                logger.info(f"Updated world state for session: {session_id}")
                return True

        except Exception as e:
            logger.error(f"Error updating world state: {e}")
            return False

    def discover_location(self, session_id: str, location_name: str) -> Optional[Location]:
        """
        Handle location discovery.

        Args:
            session_id: Game session ID
            location_name: Name of location to discover

        Returns:
            Location: Discovered location or None if not found
        """
        try:
            # Get world state
            world_context = self._get_world_context(session_id)
            if not world_context:
                return None

            locations = world_context.world_state.get("locations", {})

            if location_name not in locations:
                # Generate new location if it doesn't exist
                location = self._generate_random_location()
                locations[location_name] = self._location_to_dict(location)

                # Update world state
                self.update_world_state(session_id, {"locations": locations})

                logger.info(f"Generated new location: {location_name}")
                return location
            else:
                # Convert dict back to Location object
                loc_data = locations[location_name]
                location = Location(
                    name=loc_data["name"],
                    location_type=LocationType(loc_data["location_type"]),
                    description=loc_data["description"],
                    connections=loc_data.get("connections", []),
                    npcs=loc_data.get("npcs", []),
                    items=loc_data.get("items", []),
                    events=loc_data.get("events", []),
                    atmosphere=loc_data.get("atmosphere", "neutral"),
                    danger_level=loc_data.get("danger_level", 1)
                )

                logger.info(f"Discovered existing location: {location_name}")
                return location

        except Exception as e:
            logger.error(f"Error discovering location: {e}")
            return None

    def process_time_passage(self, session_id: str, hours: int = 1):
        """
        Process the passage of time in the world.

        Args:
            session_id: Game session ID
            hours: Number of hours that have passed
        """
        try:
            # Update time cycle
            world_context = self._get_world_context(session_id)
            if not world_context:
                return

            current_cycle = world_context.world_state.get("time_cycle", 0)
            new_cycle = current_cycle + hours

            # Update time of day
            time_of_day = self._calculate_time_of_day(new_cycle)

            # Process background events
            self._process_background_events(session_id, hours)

            # Update world state
            changes = {
                "time_cycle": new_cycle,
                "time_of_day": time_of_day
            }

            # Random weather changes
            if random.random() < 0.1:  # 10% chance per hour
                changes["weather"] = self._generate_weather()

            self.update_world_state(session_id, changes)

            logger.info(f"Processed {hours} hours of time passage")

        except Exception as e:
            logger.error(f"Error processing time passage: {e}")

    def generate_random_event(self, session_id: str, location: str = None) -> Optional[WorldEvent]:
        """
        Generate a random event for the world.

        Args:
            session_id: Game session ID
            location: Optional specific location for the event

        Returns:
            WorldEvent: Generated event or None if generation failed
        """
        try:
            event_type = random.choice(list(EventType))

            if event_type in self.event_templates:
                event_name = random.choice(self.event_templates[event_type])
            else:
                event_name = "mysterious occurrence"

            # Generate event description
            description = self._generate_event_description(event_name, event_type, location)

            # Create event
            event = WorldEvent(
                name=event_name,
                event_type=event_type,
                description=description,
                location=location or "unknown",
                duration_hours=random.randint(1, 24),
                priority=random.randint(1, 10)
            )

            # Save to database
            self._save_event_to_db(session_id, event)

            logger.info(f"Generated random event: {event_name}")
            return event

        except Exception as e:
            logger.error(f"Error generating random event: {e}")
            return None

    def get_location_description(self, session_id: str, location_name: str) -> str:
        """
        Get detailed description of a location.

        Args:
            session_id: Game session ID
            location_name: Name of the location

        Returns:
            str: Location description
        """
        try:
            world_context = self._get_world_context(session_id)
            if not world_context:
                return "You find yourself in an unknown place."

            locations = world_context.world_state.get("locations", {})

            if location_name not in locations:
                return f"You cannot see {location_name} from here."

            location_data = locations[location_name]

            # Build comprehensive description
            description_parts = [
                location_data["description"],
                f"Atmosphere: {location_data.get('atmosphere', 'neutral')}",
                f"Time of day: {world_context.time_of_day}",
                f"Weather: {world_context.weather}"
            ]

            # Add NPCs if present
            if location_data.get("npcs"):
                description_parts.append(f"People here: {', '.join(location_data['npcs'])}")

            # Add available connections
            if location_data.get("connections"):
                description_parts.append(f"You can travel to: {', '.join(location_data['connections'])}")

            return "\n".join(description_parts)

        except Exception as e:
            logger.error(f"Error getting location description: {e}")
            return "The area is shrouded in mystery."

    def _generate_world_name(self) -> str:
        """Generate a random world name."""
        prefixes = ["Aether", "Mystic", "Shadow", "Crystal", "Ancient", "Lost", "Forgotten"]
        suffixes = ["realm", "lands", "kingdom", "world", "domain", "empire", "territories"]

        return f"{random.choice(prefixes)} {random.choice(suffixes)}"

    def _generate_location(self, location_type: LocationType, name: str = None) -> Location:
        """Generate a location of the specified type."""
        if not name:
            name = self._generate_location_name(location_type)

        template = self.location_templates.get(location_type, {})

        # Generate description
        description = self._generate_location_description(name, location_type, template)

        # Generate features
        features = template.get("features", [])
        atmosphere = random.choice(template.get("atmospheres", ["neutral"]))

        return Location(
            name=name,
            location_type=location_type,
            description=description,
            atmosphere=atmosphere,
            danger_level=self._calculate_danger_level(location_type)
        )

    def _generate_location_name(self, location_type: LocationType) -> str:
        """Generate a name for a location."""
        template = self.location_templates.get(location_type, {})
        names = template.get("names", ["Unknown Place"])
        return random.choice(names)

    def _generate_location_description(self, name: str, location_type: LocationType, template: Dict) -> str:
        """Generate a description for a location."""
        prompt = f"""
        Generate a vivid description for a {location_type.value} called {name}.

        Include:
        - Visual details and atmosphere
        - Notable features or landmarks
        - Sounds, smells, and sensory details
        - What makes this place unique

        Keep it to 2-3 sentences and make it immersive.
        """

        try:
            response = self.llm.invoke([SystemMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Error generating location description: {e}")
            return f"A {location_type.value} with an unremarkable appearance."

    def _generate_connected_locations(self, starting_location: Location) -> List[Location]:
        """Generate locations connected to the starting location."""
        locations = [starting_location]

        # Generate 3-5 connected locations
        num_locations = random.randint(3, 5)

        for _ in range(num_locations):
            location_type = random.choice(list(LocationType))
            location = self._generate_location(location_type)

            # Connect to starting location
            starting_location.connections.append(location.name)
            location.connections.append(starting_location.name)

            locations.append(location)

        return locations

    def _generate_random_location(self) -> Location:
        """Generate a random location."""
        location_type = random.choice(list(LocationType))
        return self._generate_location(location_type)

    def _calculate_danger_level(self, location_type: LocationType) -> int:
        """Calculate danger level based on location type."""
        danger_levels = {
            LocationType.VILLAGE: 1,
            LocationType.CITY: 2,
            LocationType.FOREST: 4,
            LocationType.MOUNTAIN: 5,
            LocationType.CAVE: 6,
            LocationType.DUNGEON: 8,
            LocationType.RUINS: 7,
            LocationType.WILDERNESS: 5
        }

        base_level = danger_levels.get(location_type, 3)
        return min(10, max(1, base_level + random.randint(-1, 2)))

    def _location_to_dict(self, location: Location) -> Dict[str, Any]:
        """Convert Location object to dictionary."""
        return {
            "name": location.name,
            "location_type": location.location_type.value,
            "description": location.description,
            "connections": location.connections,
            "npcs": location.npcs,
            "items": location.items,
            "events": location.events,
            "atmosphere": location.atmosphere,
            "danger_level": location.danger_level,
            "discovery_requirements": location.discovery_requirements
        }

    def _save_world_to_db(self, session_id: str, world_name: str, world_context: WorldContext):
        """Save world to database."""
        try:
            with self.db_manager.get_session() as session:
                world_record = World(
                    session_id=session_id,
                    name=world_name,
                    description=f"A dynamically generated world for session {session_id}",
                    current_location=world_context.current_location,
                    available_locations=world_context.available_locations,
                    world_state=world_context.world_state,
                    time_of_day=world_context.time_of_day,
                    weather=world_context.weather,
                    atmosphere=world_context.atmosphere,
                    active_events=world_context.current_events
                )
                session.add(world_record)

        except Exception as e:
            logger.error(f"Error saving world to database: {e}")

    def _get_world_context(self, session_id: str) -> Optional[WorldContext]:
        """Get world context from database."""
        try:
            with self.db_manager.get_session() as session:
                world_record = session.query(World).filter_by(
                    session_id=session_id
                ).first()

                if not world_record:
                    return None

                return WorldContext(
                    current_location=world_record.current_location,
                    available_locations=world_record.available_locations or [],
                    active_npcs=[],  # Would be populated from character data
                    current_events=world_record.active_events or [],
                    world_state=world_record.world_state or {},
                    time_of_day=world_record.time_of_day,
                    weather=world_record.weather,
                    atmosphere=world_record.atmosphere
                )

        except Exception as e:
            logger.error(f"Error getting world context: {e}")
            return None

    def _generate_background_events(self, session_id: str, locations: List[Location]):
        """Generate initial background events."""
        for location in locations:
            if random.random() < 0.3:  # 30% chance per location
                event = self.generate_random_event(session_id, location.name)
                if event:
                    location.events.append(event.name)

    def _process_background_events(self, session_id: str, hours: int):
        """Process background events over time."""
        try:
            with self.db_manager.get_session() as session:
                # Get active events
                events = session.query(Event).filter_by(
                    session_id=session_id,
                    is_active=True
                ).all()

                for event in events:
                    # Decrease remaining duration
                    if event.duration_hours:
                        event.duration_hours -= hours

                        # End event if duration expired
                        if event.duration_hours <= 0:
                            event.is_active = False
                            event.end_time = datetime.now()

                # Chance to generate new random events
                if random.random() < 0.1 * hours:  # 10% chance per hour
                    self.generate_random_event(session_id)

        except Exception as e:
            logger.error(f"Error processing background events: {e}")

    def _calculate_time_of_day(self, time_cycle: int) -> str:
        """Calculate time of day from time cycle."""
        hour = time_cycle % 24

        if 6 <= hour < 12:
            return "morning"
        elif 12 <= hour < 18:
            return "afternoon"
        elif 18 <= hour < 22:
            return "evening"
        else:
            return "night"

    def _generate_weather(self) -> str:
        """Generate random weather."""
        weather_options = [
            "clear", "cloudy", "overcast", "light rain", "heavy rain",
            "drizzle", "fog", "windy", "stormy", "snow", "sunny"
        ]
        return random.choice(weather_options)

    def _generate_event_description(self, event_name: str, event_type: EventType, location: str = None) -> str:
        """Generate description for an event."""
        prompt = f"""
        Generate a brief description for a {event_type.value} event called "{event_name}".
        {f"This event is happening in {location}." if location else ""}

        Include:
        - What is happening
        - Who might be involved
        - Potential impact on the area

        Keep it to 1-2 sentences.
        """

        try:
            response = self.llm.invoke([SystemMessage(content=prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Error generating event description: {e}")
            return f"A {event_type.value} event is occurring."

    def _save_event_to_db(self, session_id: str, event: WorldEvent):
        """Save event to database."""
        try:
            with self.db_manager.get_session() as session:
                event_record = Event(
                    session_id=session_id,
                    name=event.name,
                    description=event.description,
                    event_type=event.event_type.value,
                    location=event.location,
                    is_active=event.is_active,
                    priority=event.priority,
                    involved_characters=event.participants,
                    world_effects=event.effects,
                    duration_hours=event.duration_hours,
                    world_id=1  # Default world ID
                )
                session.add(event_record)

        except Exception as e:
            logger.error(f"Error saving event to database: {e}")
