"""
Command-line interface for the RPG engine.

This module provides a CLI for interacting with the RPG engine
for development, testing, and standalone usage.
"""

import logging
import os
import sys
from typing import Optional
import uuid

import typer
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt
from rich.text import Text
from rich.table import Table

from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langchain_anthropic import ChatAnthropic

from ..core.game_engine import RPGEngine
from ..database import init_database

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise in CLI
logger = logging.getLogger(__name__)

# Create CLI app
app = typer.Typer(
    name="rpg-engine",
    help="Collaborative story creation engine built with LangGraph"
)

# Rich console for pretty output
console = Console()


def get_llm():
    """Get configured LLM instance."""
    llm_provider = os.getenv("LLM_PROVIDER", "openai")
    
    if llm_provider == "openai":
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            console.print("[red]Error: OPENAI_API_KEY environment variable not set[/red]")
            raise typer.Exit(1)
        
        return ChatOpenAI(
            model=os.getenv("OPENAI_MODEL", "gpt-4"),
            api_key=api_key
        )
    
    elif llm_provider == "anthropic":
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if not api_key:
            console.print("[red]Error: ANTHROPIC_API_KEY environment variable not set[/red]")
            raise typer.Exit(1)
        
        return ChatAnthropic(
            model=os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229"),
            api_key=api_key
        )
    
    else:
        console.print(f"[red]Error: Unsupported LLM provider: {llm_provider}[/red]")
        raise typer.Exit(1)


@app.command()
def init_db(
    database_url: str = typer.Option(
        "sqlite:///rpg_game.db",
        "--database-url",
        "-d",
        help="Database URL"
    ),
    reset: bool = typer.Option(
        False,
        "--reset",
        "-r",
        help="Reset database (WARNING: This will delete all data)"
    )
):
    """Initialize the database."""
    try:
        if reset:
            confirm = Prompt.ask(
                "[red]This will delete all existing data. Are you sure?[/red]",
                choices=["yes", "no"],
                default="no"
            )
            if confirm != "yes":
                console.print("Database reset cancelled.")
                return
        
        init_database(database_url, reset=reset)
        console.print(f"[green]Database initialized successfully![/green]")
        console.print(f"Database URL: {database_url}")
        
    except Exception as e:
        console.print(f"[red]Error initializing database: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def play(
    user_id: str = typer.Option(
        None,
        "--user-id",
        "-u",
        help="User ID (generated if not provided)"
    ),
    session_id: str = typer.Option(
        None,
        "--session-id",
        "-s",
        help="Session ID (generated if not provided)"
    ),
    database_url: str = typer.Option(
        "sqlite:///rpg_game.db",
        "--database-url",
        "-d",
        help="Database URL"
    )
):
    """Start an interactive RPG session."""
    try:
        # Generate IDs if not provided
        if not user_id:
            user_id = f"user_{uuid.uuid4().hex[:8]}"
        if not session_id:
            session_id = f"session_{uuid.uuid4().hex[:8]}"
        
        console.print(Panel.fit(
            f"[bold blue]RPG Engine - Interactive Session[/bold blue]\n"
            f"User ID: {user_id}\n"
            f"Session ID: {session_id}",
            border_style="blue"
        ))
        
        # Initialize engine
        console.print("[yellow]Initializing RPG engine...[/yellow]")
        llm = get_llm()
        engine = RPGEngine(
            llm=llm,
            database_url=database_url
        )
        
        # Start game
        console.print("[yellow]Starting new game...[/yellow]")
        engine.start_new_game(user_id, session_id)
        
        # Get initial message
        initial_response = engine.process_user_input(session_id, "")
        console.print(Panel(initial_response, title="Game Master", border_style="green"))
        
        # Interactive loop
        console.print("\n[dim]Type 'quit' to exit, 'help' for commands[/dim]\n")
        
        while True:
            try:
                user_input = Prompt.ask("[bold cyan]You[/bold cyan]")
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    console.print("[yellow]Thanks for playing![/yellow]")
                    break
                
                elif user_input.lower() == 'help':
                    show_help()
                    continue
                
                elif user_input.lower() == 'state':
                    show_game_state(engine, session_id)
                    continue
                
                elif user_input.lower() == 'memory':
                    show_memory_stats(engine, session_id)
                    continue
                
                # Process game input
                console.print("[yellow]Processing...[/yellow]")
                response = engine.process_user_input(session_id, user_input)
                console.print(Panel(response, title="Game Master", border_style="green"))
                
            except KeyboardInterrupt:
                console.print("\n[yellow]Game interrupted. Thanks for playing![/yellow]")
                break
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")
                continue
        
    except Exception as e:
        console.print(f"[red]Error starting game: {e}[/red]")
        raise typer.Exit(1)


def show_help():
    """Show help information."""
    help_table = Table(title="Available Commands")
    help_table.add_column("Command", style="cyan")
    help_table.add_column("Description", style="white")
    
    help_table.add_row("quit/exit/q", "Exit the game")
    help_table.add_row("help", "Show this help message")
    help_table.add_row("state", "Show current game state")
    help_table.add_row("memory", "Show memory statistics")
    
    console.print(help_table)


def show_game_state(engine: RPGEngine, session_id: str):
    """Show current game state."""
    try:
        state = engine.get_game_state(session_id)
        if not state:
            console.print("[red]No game state found[/red]")
            return
        
        state_table = Table(title="Game State")
        state_table.add_column("Property", style="cyan")
        state_table.add_column("Value", style="white")
        
        state_table.add_row("Game Phase", str(state.get("game_phase", "Unknown")))
        state_table.add_row("Action Type", str(state.get("action_type", "Unknown")))
        
        # Character info
        character = state.get("player_character")
        if character:
            state_table.add_row("Character Name", character.get("name", "Unknown"))
            state_table.add_row("Location", character.get("current_location", "Unknown"))
            state_table.add_row("Health", str(character.get("health", "Unknown")))
        
        # World info
        world = state.get("world_context", {})
        state_table.add_row("World Location", world.get("current_location", "Unknown"))
        state_table.add_row("Time of Day", world.get("time_of_day", "Unknown"))
        state_table.add_row("Weather", world.get("weather", "Unknown"))
        
        console.print(state_table)
        
    except Exception as e:
        console.print(f"[red]Error getting game state: {e}[/red]")


def show_memory_stats(engine: RPGEngine, session_id: str):
    """Show memory statistics."""
    try:
        stats = engine.memory_manager.get_memory_stats(session_id)
        
        if "error" in stats:
            console.print(f"[red]Error: {stats['error']}[/red]")
            return
        
        memory_table = Table(title="Memory Statistics")
        memory_table.add_column("Metric", style="cyan")
        memory_table.add_column("Value", style="white")
        
        memory_table.add_row("Total Memories", str(stats.get("total_memories", 0)))
        memory_table.add_row("Average Importance", f"{stats.get('average_importance', 0):.2f}")
        
        # Memory types
        memory_types = stats.get("memory_types", {})
        for mem_type, count in memory_types.items():
            memory_table.add_row(f"  {mem_type.title()}", str(count))
        
        # Most accessed
        most_accessed = stats.get("most_accessed")
        if most_accessed:
            memory_table.add_row("Most Accessed", most_accessed.get("title", "Unknown"))
            memory_table.add_row("Access Count", str(most_accessed.get("access_count", 0)))
        
        console.print(memory_table)
        
    except Exception as e:
        console.print(f"[red]Error getting memory stats: {e}[/red]")


@app.command()
def server(
    host: str = typer.Option("0.0.0.0", "--host", "-h", help="Host to bind to"),
    port: int = typer.Option(8000, "--port", "-p", help="Port to bind to"),
    reload: bool = typer.Option(False, "--reload", "-r", help="Enable auto-reload")
):
    """Start the API server."""
    try:
        import uvicorn
        from ..api.main import app as fastapi_app
        
        console.print(f"[green]Starting RPG Engine API server on {host}:{port}[/green]")
        console.print(f"[dim]API docs will be available at http://{host}:{port}/docs[/dim]")
        
        uvicorn.run(
            "rpg_engine.api.main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
        
    except ImportError:
        console.print("[red]Error: uvicorn not installed. Install with: pip install uvicorn[/red]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error starting server: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def version():
    """Show version information."""
    console.print("[bold blue]RPG Engine v1.0.0[/bold blue]")
    console.print("Collaborative story creation engine built with LangGraph")


if __name__ == "__main__":
    app()
